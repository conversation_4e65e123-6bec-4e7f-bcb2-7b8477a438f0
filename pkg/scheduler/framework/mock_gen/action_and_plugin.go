// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/scheduler/framework/interface.go

// Package mock_framework is a generated GoMock package.
package mock_framework

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	framework "volcano.sh/volcano/pkg/scheduler/framework"
)

// MockAction is a mock of Action interface.
type MockAction struct {
	ctrl     *gomock.Controller
	recorder *MockActionMockRecorder
}

// MockActionMockRecorder is the mock recorder for MockAction.
type MockActionMockRecorder struct {
	mock *MockAction
}

// NewMockAction creates a new mock instance.
func NewMockAction(ctrl *gomock.Controller) *MockAction {
	mock := &MockAction{ctrl: ctrl}
	mock.recorder = &MockActionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAction) EXPECT() *MockActionMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockAction) Execute(ssn *framework.Session) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Execute", ssn)
}

// Execute indicates an expected call of Execute.
func (mr *MockActionMockRecorder) Execute(ssn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockAction)(nil).Execute), ssn)
}

// Initialize mocks base method.
func (m *MockAction) Initialize() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Initialize")
}

// Initialize indicates an expected call of Initialize.
func (mr *MockActionMockRecorder) Initialize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Initialize", reflect.TypeOf((*MockAction)(nil).Initialize))
}

// Name mocks base method.
func (m *MockAction) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockActionMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockAction)(nil).Name))
}

// UnInitialize mocks base method.
func (m *MockAction) UnInitialize() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UnInitialize")
}

// UnInitialize indicates an expected call of UnInitialize.
func (mr *MockActionMockRecorder) UnInitialize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnInitialize", reflect.TypeOf((*MockAction)(nil).UnInitialize))
}

// MockPlugin is a mock of Plugin interface.
type MockPlugin struct {
	ctrl     *gomock.Controller
	recorder *MockPluginMockRecorder
}

// MockPluginMockRecorder is the mock recorder for MockPlugin.
type MockPluginMockRecorder struct {
	mock *MockPlugin
}

// NewMockPlugin creates a new mock instance.
func NewMockPlugin(ctrl *gomock.Controller) *MockPlugin {
	mock := &MockPlugin{ctrl: ctrl}
	mock.recorder = &MockPluginMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlugin) EXPECT() *MockPluginMockRecorder {
	return m.recorder
}

// Name mocks base method.
func (m *MockPlugin) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockPluginMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockPlugin)(nil).Name))
}

// OnSessionClose mocks base method.
func (m *MockPlugin) OnSessionClose(ssn *framework.Session) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OnSessionClose", ssn)
}

// OnSessionClose indicates an expected call of OnSessionClose.
func (mr *MockPluginMockRecorder) OnSessionClose(ssn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSessionClose", reflect.TypeOf((*MockPlugin)(nil).OnSessionClose), ssn)
}

// OnSessionOpen mocks base method.
func (m *MockPlugin) OnSessionOpen(ssn *framework.Session) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "OnSessionOpen", ssn)
}

// OnSessionOpen indicates an expected call of OnSessionOpen.
func (mr *MockPluginMockRecorder) OnSessionOpen(ssn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSessionOpen", reflect.TypeOf((*MockPlugin)(nil).OnSessionOpen), ssn)
}
