/*
Copyright 2022 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package rescheduling

import "time"

// lastRescheduleTime records the last execution time.
var lastRescheduleTime time.Time

func init() {
	lastRescheduleTime = time.Now()
}

// timeToRun checks whether it is time to execute rescheduling
func timeToRun(interval time.Duration) bool {
	now := time.Now()
	if lastRescheduleTime.Add(interval).Before(now) {
		lastRescheduleTime = now
		return true
	}
	return false
}
