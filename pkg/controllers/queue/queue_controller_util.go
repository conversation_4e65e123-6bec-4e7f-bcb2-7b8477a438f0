/*
Copyright 2019 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package queue

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	schedulingv1beta1 "volcano.sh/apis/pkg/apis/scheduling/v1beta1"
)

// IsQueueReference return if ownerReference is Queue Kind.
func IsQueueReference(ref *metav1.OwnerReference) bool {
	if ref == nil {
		return false
	}

	if ref.APIVersion != schedulingv1beta1.SchemeGroupVersion.String() {
		return false
	}

	if ref.Kind != "Queue" {
		return false
	}

	return true
}
