/*
Copyright 2019 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ssh

const (
	// SSHPrivateKey private key
	SSHPrivateKey = "id_rsa"

	// SSHPublicKey public key
	SSHPublicKey = "id_rsa.pub"

	// SSHAuthorizedKeys authkey
	SSHAuthorizedKeys = "authorized_keys"

	// SSHConfig  ssh conf
	SSHConfig = "config"

	// SSHAbsolutePath ssh abs path
	SSHAbsolutePath = "/root/.ssh"

	// SSHRelativePath ssh rel path
	SSHRelativePath = ".ssh"
)
