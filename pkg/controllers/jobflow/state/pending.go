/*
Copyright 2022 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package state

import (
	jobflowv1alpha1 "volcano.sh/apis/pkg/apis/flow/v1alpha1"
)

type pendingState struct {
	jobFlow *jobflowv1alpha1.JobFlow
}

func (p *pendingState) Execute(action jobflowv1alpha1.Action) error {
	switch action {
	case jobflowv1alpha1.SyncJobFlowAction:
		return SyncJobFlow(p.jobFlow, func(status *jobflowv1alpha1.JobFlowStatus, allJobList int) {
			if (len(status.RunningJobs) > 0 || len(status.CompletedJobs) > 0) && len(status.FailedJobs) <= 0 {
				status.State.Phase = jobflowv1alpha1.Running
			} else if len(status.FailedJobs) > 0 {
				status.State.Phase = jobflowv1alpha1.Failed
			} else {
				status.State.Phase = jobflowv1alpha1.Pending
			}
		})
	}
	return nil
}
