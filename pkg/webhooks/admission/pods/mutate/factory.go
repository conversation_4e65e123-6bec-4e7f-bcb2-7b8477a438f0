/*
Copyright 2021 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package mutate

import (
	v1 "k8s.io/api/core/v1"

	wkconfig "volcano.sh/volcano/pkg/webhooks/config"
)

// ResGroup interface for resource group
type ResGroup interface {
	IsBelongResGroup(pod *v1.Pod, resGroupConfig wkconfig.ResGroupConfig) bool
}

// GetResGroup return the interface besed on resourceGroup.Object.Key
func GetResGroup(resourceGroup wkconfig.ResGroupConfig) ResGroup {
	switch resourceGroup.Object.Key {
	case "namespace":
		return NewNamespaceResGroup()
	case "annotation":
		return NewAnnotationResGroup()
	}
	return NewAnnotationResGroup()
}
