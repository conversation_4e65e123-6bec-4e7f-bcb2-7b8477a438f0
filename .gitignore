# OSX leaves these everywhere on SMB shares
._*

# OSX trash
.DS_Store

# Eclipse files
.classpath
.project
.settings/**

# Files generated by JetBrains IDEs, e.g. IntelliJ IDEA
.idea/
*.iml

# Vscode files
.vscode

# This is where the result of the go build goes
/output*/
/_output*/
/_output

# Emacs save files
*~
\#*\#
.\#*

# Vim-related files
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist

# cscope-related files
cscope.*

# Go test binaries
*.test
/hack/.test-cmd-auth

# JUnit test output from ginkgo e2e tests
/junit*.xml

# Mercurial files
**/.hg
**/.hg*

# Vagrant
.vagrant
network_closure.sh

# Local cluster env variables
/cluster/env.sh

# Compiled binaries in third_party
/third_party/pkg

# Also ignore etcd installed by hack/install-etcd.sh
/third_party/etcd*
/default.etcd

# User cluster configs
.kubeconfig

.tags*

# Version file for dockerized build
.dockerized-kube-version-defs

# Web UI
/www/master/node_modules/
/www/master/npm-debug.log
/www/master/shared/config/development.json

# Karma output
/www/test_out

# precommit temporary directories created by ./hack/verify-generated-docs.sh and ./hack/lib/util.sh
/_tmp/
/doc_tmp/

# Test artifacts produced by Jenkins jobs
/_artifacts/

# Go dependencies installed on Jenkins
/_gopath/

# Config directories created by gcloud and gsutil on Jenkins
/.config/gcloud*/
/.gsutil/

# CoreOS stuff
/cluster/libvirt-coreos/coreos_*.img

# Juju Stuff
/cluster/juju/charms/*
/cluster/juju/bundles/local.yaml

# Downloaded Kubernetes binary release
/kubernetes/

# direnv .envrc files
.envrc

# Downloaded kubernetes binary release tar ball
kubernetes.tar.gz

# generated files in any directory
# TODO(thockin): uncomment this when we stop committing the generated files.
#zz_generated.*
#zz_generated.openapi.go

# make-related metadata
/.make/
# Just in time generated data in the source, should never be commited
/test/e2e/generated/bindata.go

# This file used by some vendor repos (e.g. github.com/go-openapi/...) to store secret variables and should not be ignored
!\.drone\.sec

/bazel-*
*.pyc

# e2e log files
*.log

# test coverage file
coverage.txt

# go vendor directory
vendor

# helm dependency files
installer/helm/chart/volcano/requirements.lock
