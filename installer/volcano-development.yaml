apiVersion: v1
kind: Namespace
metadata:
  name: volcano-system
  labels:
    kubernetes.io/metadata.name: volcano-system
---
apiVersion: v1
kind: Namespace
metadata:
  name: volcano-monitoring
  labels:
    kubernetes.io/metadata.name: volcano-monitoring
---
# Source: volcano/templates/admission.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: volcano-admission
  namespace: volcano-system
---
# Source: volcano/templates/admission.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: volcano-admission-configmap
  namespace: volcano-system
data:
  volcano-admission.conf: |
    #resourceGroups:
    #- resourceGroup: management                    # set the resource group name
    #  object:
    #    key: namespace                             # set the field and the value to be matched
    #    value:
    #    - mng-ns-1
    #  schedulerName: default-scheduler             # set the scheduler for patching
    #  tolerations:                                 # set the tolerations for patching
    #  - effect: NoSchedule
    #    key: taint
    #    operator: Exists
    #  labels:
    #    volcano.sh/nodetype: management           # set the nodeSelector for patching
    #- resourceGroup: cpu
    #  object:
    #    key: annotation
    #    value:
    #    - "volcano.sh/resource-group: cpu"
    #  schedulerName: volcano
    #  labels:
    #    volcano.sh/nodetype: cpu
    #- resourceGroup: gpu                          # if the object is unsetted, default is:  the key is annotation,
    #  schedulerName: volcano                      # the annotation key is fixed and is "volcano.sh/resource-group", The corresponding value is the resourceGroup field
    #  labels:
    #    volcano.sh/nodetype: gpu
---
# Source: volcano/templates/admission.yaml
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-admission
rules:
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations", "validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "create", "update"]
  # Rules below is used generate admission service secret
  - apiGroups: ["certificates.k8s.io"]
    resources: ["certificatesigningrequests"]
    verbs: ["get", "list", "create", "delete"]
  - apiGroups: ["certificates.k8s.io"]
    resources: ["certificatesigningrequests/approval"]
    verbs: ["create", "update"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create", "get", "patch"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups"]
    verbs: ["get", "list", "watch"]
---
# Source: volcano/templates/admission.yaml
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-admission-role
subjects:
  - kind: ServiceAccount
    name: volcano-admission
    namespace: volcano-system
roleRef:
  kind: ClusterRole
  name: volcano-admission
  apiGroup: rbac.authorization.k8s.io
---
# Source: volcano/templates/admission.yaml
apiVersion: v1
kind: Service
metadata:
  labels:
    app: volcano-admission
  name: volcano-admission-service
  namespace: volcano-system
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 8443
  selector:
    app: volcano-admission
  sessionAffinity: None
---
# Source: volcano/templates/admission.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: volcano-admission
  name: volcano-admission
  namespace: volcano-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: volcano-admission
  template:
    metadata:
      labels:
        app: volcano-admission
    spec:
      securityContext:
        seLinuxOptions:
          level: s0:c123,c456
        seccompProfile:
          type: RuntimeDefault
      serviceAccount: volcano-admission
      priorityClassName: system-cluster-critical
      containers:
        - args:
            - --enabled-admission=/jobs/mutate,/jobs/validate,/podgroups/mutate,/pods/validate,/pods/mutate,/queues/mutate,/queues/validate
            - --tls-cert-file=/admission.local.config/certificates/tls.crt
            - --tls-private-key-file=/admission.local.config/certificates/tls.key
            - --ca-cert-file=/admission.local.config/certificates/ca.crt
            - --admission-conf=/admission.local.config/configmap/volcano-admission.conf
            - --webhook-namespace=volcano-system
            - --webhook-service-name=volcano-admission-service
            - --enable-healthz=true
            - --logtostderr
            - --port=8443
            - -v=4
            - 2>&1
          image: volcanosh/vc-webhook-manager:v1.9.1
          imagePullPolicy: Always
          name: admission
          volumeMounts:
            - mountPath: /admission.local.config/certificates
              name: admission-certs
              readOnly: true
            - mountPath: /admission.local.config/configmap
              name: admission-config
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              add:
              - DAC_OVERRIDE
              drop:
              - ALL
            runAsNonRoot: true
            runAsUser: 1000
      volumes:
        - name: admission-certs
          secret:
            defaultMode: 420
            secretName: volcano-admission-secret
        - name: admission-config
          configMap:
            name: volcano-admission-configmap
---
# Source: volcano/templates/admission.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: volcano-admission-init
  namespace: volcano-system
  labels:
    app: volcano-admission-init
spec:
  backoffLimit: 3
  template:
    spec:
      securityContext:
        seLinuxOptions:
          level: s0:c123,c456
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: volcano-admission
      priorityClassName: system-cluster-critical
      restartPolicy: Never
      containers:
        - name: main
          image: volcanosh/vc-webhook-manager:v1.9.1
          imagePullPolicy: Always
          command: ["./gen-admission-secret.sh", "--service", "volcano-admission-service", "--namespace",
                    "volcano-system", "--secret", "volcano-admission-secret"]
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              add:
              - DAC_OVERRIDE
              drop:
              - ALL
            runAsNonRoot: true
            runAsUser: 1000
---
# Source: volcano/templates/batch_v1alpha1_job.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: jobs.batch.volcano.sh
spec:
  group: batch.volcano.sh
  names:
    kind: Job
    listKind: JobList
    plural: jobs
    shortNames:
    - vcjob
    - vj
    singular: job
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: STATUS
      type: string
    - jsonPath: .status.minAvailable
      name: minAvailable
      type: integer
    - jsonPath: .status.running
      name: RUNNINGS
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    - jsonPath: .spec.queue
      name: QUEUE
      priority: 1
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              maxRetry:
                format: int32
                type: integer
              minAvailable:
                format: int32
                type: integer
              minSuccess:
                format: int32
                minimum: 1
                type: integer
              plugins:
                additionalProperties:
                  items:
                    type: string
                  type: array
                type: object
              policies:
                items:
                  properties:
                    action:
                      type: string
                    event:
                      type: string
                    events:
                      items:
                        type: string
                      type: array
                    exitCode:
                      format: int32
                      type: integer
                    timeout:
                      type: string
                  type: object
                type: array
              priorityClassName:
                type: string
              queue:
                type: string
              runningEstimate:
                type: string
              schedulerName:
                type: string
              tasks:
                items:
                  properties:
                    dependsOn:
                      properties:
                        iteration:
                          type: string
                        name:
                          items:
                            type: string
                          type: array
                      type: object
                    maxRetry:
                      format: int32
                      type: integer
                    minAvailable:
                      format: int32
                      type: integer
                    name:
                      type: string
                    policies:
                      items:
                        properties:
                          action:
                            type: string
                          event:
                            type: string
                          events:
                            items:
                              type: string
                            type: array
                          exitCode:
                            format: int32
                            type: integer
                          timeout:
                            type: string
                        type: object
                      type: array
                    replicas:
                      format: int32
                      type: integer
                    template:
                      properties:
                        metadata:
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              type: object
                            finalizers:
                              items:
                                type: string
                              type: array
                            labels:
                              additionalProperties:
                                type: string
                              type: object
                            name:
                              type: string
                            namespace:
                              type: string
                          type: object
                        spec:
                          properties:
                            activeDeadlineSeconds:
                              format: int64
                              type: integer
                            affinity:
                              properties:
                                nodeAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          preference:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchFields:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - preference
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      properties:
                                        nodeSelectorTerms:
                                          items:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchFields:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                            type: object
                                          type: array
                                      required:
                                      - nodeSelectorTerms
                                      type: object
                                  type: object
                                podAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          podAffinityTerm:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              matchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              mismatchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              namespaceSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              namespaces:
                                                items:
                                                  type: string
                                                type: array
                                              topologyKey:
                                                type: string
                                            required:
                                            - topologyKey
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - podAffinityTerm
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          labelSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          matchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          mismatchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          namespaceSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          namespaces:
                                            items:
                                              type: string
                                            type: array
                                          topologyKey:
                                            type: string
                                        required:
                                        - topologyKey
                                        type: object
                                      type: array
                                  type: object
                                podAntiAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          podAffinityTerm:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              matchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              mismatchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              namespaceSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              namespaces:
                                                items:
                                                  type: string
                                                type: array
                                              topologyKey:
                                                type: string
                                            required:
                                            - topologyKey
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - podAffinityTerm
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          labelSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          matchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          mismatchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          namespaceSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          namespaces:
                                            items:
                                              type: string
                                            type: array
                                          topologyKey:
                                            type: string
                                        required:
                                        - topologyKey
                                        type: object
                                      type: array
                                  type: object
                              type: object
                            automountServiceAccountToken:
                              type: boolean
                            containers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            dnsConfig:
                              properties:
                                nameservers:
                                  items:
                                    type: string
                                  type: array
                                options:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    type: object
                                  type: array
                                searches:
                                  items:
                                    type: string
                                  type: array
                              type: object
                            dnsPolicy:
                              type: string
                            enableServiceLinks:
                              type: boolean
                            ephemeralContainers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  targetContainerName:
                                    type: string
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            hostAliases:
                              items:
                                properties:
                                  hostnames:
                                    items:
                                      type: string
                                    type: array
                                  ip:
                                    type: string
                                type: object
                              type: array
                            hostIPC:
                              type: boolean
                            hostNetwork:
                              type: boolean
                            hostPID:
                              type: boolean
                            hostUsers:
                              type: boolean
                            hostname:
                              type: string
                            imagePullSecrets:
                              items:
                                properties:
                                  name:
                                    type: string
                                type: object
                              type: array
                            initContainers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            nodeName:
                              type: string
                            nodeSelector:
                              additionalProperties:
                                type: string
                              type: object
                              x-kubernetes-map-type: atomic
                            os:
                              properties:
                                name:
                                  type: string
                              required:
                              - name
                              type: object
                            overhead:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            preemptionPolicy:
                              type: string
                            priority:
                              format: int32
                              type: integer
                            priorityClassName:
                              type: string
                            readinessGates:
                              items:
                                properties:
                                  conditionType:
                                    type: string
                                required:
                                - conditionType
                                type: object
                              type: array
                            resourceClaims:
                              items:
                                properties:
                                  name:
                                    type: string
                                  source:
                                    properties:
                                      resourceClaimName:
                                        type: string
                                      resourceClaimTemplateName:
                                        type: string
                                    type: object
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            restartPolicy:
                              type: string
                            runtimeClassName:
                              type: string
                            schedulerName:
                              type: string
                            schedulingGates:
                              items:
                                properties:
                                  name:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            securityContext:
                              properties:
                                fsGroup:
                                  format: int64
                                  type: integer
                                fsGroupChangePolicy:
                                  type: string
                                runAsGroup:
                                  format: int64
                                  type: integer
                                runAsNonRoot:
                                  type: boolean
                                runAsUser:
                                  format: int64
                                  type: integer
                                seLinuxOptions:
                                  properties:
                                    level:
                                      type: string
                                    role:
                                      type: string
                                    type:
                                      type: string
                                    user:
                                      type: string
                                  type: object
                                seccompProfile:
                                  properties:
                                    localhostProfile:
                                      type: string
                                    type:
                                      type: string
                                  required:
                                  - type
                                  type: object
                                supplementalGroups:
                                  items:
                                    format: int64
                                    type: integer
                                  type: array
                                sysctls:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                windowsOptions:
                                  properties:
                                    gmsaCredentialSpec:
                                      type: string
                                    gmsaCredentialSpecName:
                                      type: string
                                    hostProcess:
                                      type: boolean
                                    runAsUserName:
                                      type: string
                                  type: object
                              type: object
                            serviceAccount:
                              type: string
                            serviceAccountName:
                              type: string
                            setHostnameAsFQDN:
                              type: boolean
                            shareProcessNamespace:
                              type: boolean
                            subdomain:
                              type: string
                            terminationGracePeriodSeconds:
                              format: int64
                              type: integer
                            tolerations:
                              items:
                                properties:
                                  effect:
                                    type: string
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  tolerationSeconds:
                                    format: int64
                                    type: integer
                                  value:
                                    type: string
                                type: object
                              type: array
                            topologySpreadConstraints:
                              items:
                                properties:
                                  labelSelector:
                                    properties:
                                      matchExpressions:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            operator:
                                              type: string
                                            values:
                                              items:
                                                type: string
                                              type: array
                                          required:
                                          - key
                                          - operator
                                          type: object
                                        type: array
                                      matchLabels:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    type: object
                                  matchLabelKeys:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  maxSkew:
                                    format: int32
                                    type: integer
                                  minDomains:
                                    format: int32
                                    type: integer
                                  nodeAffinityPolicy:
                                    type: string
                                  nodeTaintsPolicy:
                                    type: string
                                  topologyKey:
                                    type: string
                                  whenUnsatisfiable:
                                    type: string
                                required:
                                - maxSkew
                                - topologyKey
                                - whenUnsatisfiable
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - topologyKey
                              - whenUnsatisfiable
                              x-kubernetes-list-type: map
                            volumes:
                              items:
                                properties:
                                  awsElasticBlockStore:
                                    properties:
                                      fsType:
                                        type: string
                                      partition:
                                        format: int32
                                        type: integer
                                      readOnly:
                                        type: boolean
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  azureDisk:
                                    properties:
                                      cachingMode:
                                        type: string
                                      diskName:
                                        type: string
                                      diskURI:
                                        type: string
                                      fsType:
                                        type: string
                                      kind:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - diskName
                                    - diskURI
                                    type: object
                                  azureFile:
                                    properties:
                                      readOnly:
                                        type: boolean
                                      secretName:
                                        type: string
                                      shareName:
                                        type: string
                                    required:
                                    - secretName
                                    - shareName
                                    type: object
                                  cephfs:
                                    properties:
                                      monitors:
                                        items:
                                          type: string
                                        type: array
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretFile:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      user:
                                        type: string
                                    required:
                                    - monitors
                                    type: object
                                  cinder:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  configMap:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                          required:
                                          - key
                                          - path
                                          type: object
                                        type: array
                                      name:
                                        type: string
                                      optional:
                                        type: boolean
                                    type: object
                                  csi:
                                    properties:
                                      driver:
                                        type: string
                                      fsType:
                                        type: string
                                      nodePublishSecretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      readOnly:
                                        type: boolean
                                      volumeAttributes:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    required:
                                    - driver
                                    type: object
                                  downwardAPI:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                          required:
                                          - path
                                          type: object
                                        type: array
                                    type: object
                                  emptyDir:
                                    properties:
                                      medium:
                                        type: string
                                      sizeLimit:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                    type: object
                                  ephemeral:
                                    properties:
                                      volumeClaimTemplate:
                                        properties:
                                          metadata:
                                            properties:
                                              annotations:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                              finalizers:
                                                items:
                                                  type: string
                                                type: array
                                              labels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                              name:
                                                type: string
                                              namespace:
                                                type: string
                                            type: object
                                          spec:
                                            properties:
                                              accessModes:
                                                items:
                                                  type: string
                                                type: array
                                              dataSource:
                                                properties:
                                                  apiGroup:
                                                    type: string
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                required:
                                                - kind
                                                - name
                                                type: object
                                              dataSourceRef:
                                                properties:
                                                  apiGroup:
                                                    type: string
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                  namespace:
                                                    type: string
                                                required:
                                                - kind
                                                - name
                                                type: object
                                              resources:
                                                properties:
                                                  limits:
                                                    additionalProperties:
                                                      anyOf:
                                                      - type: integer
                                                      - type: string
                                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                      x-kubernetes-int-or-string: true
                                                    type: object
                                                  requests:
                                                    additionalProperties:
                                                      anyOf:
                                                      - type: integer
                                                      - type: string
                                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                      x-kubernetes-int-or-string: true
                                                    type: object
                                                type: object
                                              selector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              storageClassName:
                                                type: string
                                              volumeAttributesClassName:
                                                type: string
                                              volumeMode:
                                                type: string
                                              volumeName:
                                                type: string
                                            type: object
                                        required:
                                        - spec
                                        type: object
                                    type: object
                                  fc:
                                    properties:
                                      fsType:
                                        type: string
                                      lun:
                                        format: int32
                                        type: integer
                                      readOnly:
                                        type: boolean
                                      targetWWNs:
                                        items:
                                          type: string
                                        type: array
                                      wwids:
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  flexVolume:
                                    properties:
                                      driver:
                                        type: string
                                      fsType:
                                        type: string
                                      options:
                                        additionalProperties:
                                          type: string
                                        type: object
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                    required:
                                    - driver
                                    type: object
                                  flocker:
                                    properties:
                                      datasetName:
                                        type: string
                                      datasetUUID:
                                        type: string
                                    type: object
                                  gcePersistentDisk:
                                    properties:
                                      fsType:
                                        type: string
                                      partition:
                                        format: int32
                                        type: integer
                                      pdName:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - pdName
                                    type: object
                                  gitRepo:
                                    properties:
                                      directory:
                                        type: string
                                      repository:
                                        type: string
                                      revision:
                                        type: string
                                    required:
                                    - repository
                                    type: object
                                  glusterfs:
                                    properties:
                                      endpoints:
                                        type: string
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - endpoints
                                    - path
                                    type: object
                                  hostPath:
                                    properties:
                                      path:
                                        type: string
                                      type:
                                        type: string
                                    required:
                                    - path
                                    type: object
                                  iscsi:
                                    properties:
                                      chapAuthDiscovery:
                                        type: boolean
                                      chapAuthSession:
                                        type: boolean
                                      fsType:
                                        type: string
                                      initiatorName:
                                        type: string
                                      iqn:
                                        type: string
                                      iscsiInterface:
                                        type: string
                                      lun:
                                        format: int32
                                        type: integer
                                      portals:
                                        items:
                                          type: string
                                        type: array
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      targetPortal:
                                        type: string
                                    required:
                                    - iqn
                                    - lun
                                    - targetPortal
                                    type: object
                                  name:
                                    type: string
                                  nfs:
                                    properties:
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      server:
                                        type: string
                                    required:
                                    - path
                                    - server
                                    type: object
                                  persistentVolumeClaim:
                                    properties:
                                      claimName:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - claimName
                                    type: object
                                  photonPersistentDisk:
                                    properties:
                                      fsType:
                                        type: string
                                      pdID:
                                        type: string
                                    required:
                                    - pdID
                                    type: object
                                  portworxVolume:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  projected:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      sources:
                                        items:
                                          properties:
                                            clusterTrustBundle:
                                              properties:
                                                labelSelector:
                                                  properties:
                                                    matchExpressions:
                                                      items:
                                                        properties:
                                                          key:
                                                            type: string
                                                          operator:
                                                            type: string
                                                          values:
                                                            items:
                                                              type: string
                                                            type: array
                                                        required:
                                                        - key
                                                        - operator
                                                        type: object
                                                      type: array
                                                    matchLabels:
                                                      additionalProperties:
                                                        type: string
                                                      type: object
                                                  type: object
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                                path:
                                                  type: string
                                                signerName:
                                                  type: string
                                              required:
                                              - path
                                              type: object
                                            configMap:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                    required:
                                                    - key
                                                    - path
                                                    type: object
                                                  type: array
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              type: object
                                            downwardAPI:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      fieldRef:
                                                        properties:
                                                          apiVersion:
                                                            type: string
                                                          fieldPath:
                                                            type: string
                                                        required:
                                                        - fieldPath
                                                        type: object
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                      resourceFieldRef:
                                                        properties:
                                                          containerName:
                                                            type: string
                                                          divisor:
                                                            anyOf:
                                                            - type: integer
                                                            - type: string
                                                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                            x-kubernetes-int-or-string: true
                                                          resource:
                                                            type: string
                                                        required:
                                                        - resource
                                                        type: object
                                                    required:
                                                    - path
                                                    type: object
                                                  type: array
                                              type: object
                                            secret:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                    required:
                                                    - key
                                                    - path
                                                    type: object
                                                  type: array
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              type: object
                                            serviceAccountToken:
                                              properties:
                                                audience:
                                                  type: string
                                                expirationSeconds:
                                                  format: int64
                                                  type: integer
                                                path:
                                                  type: string
                                              required:
                                              - path
                                              type: object
                                          type: object
                                        type: array
                                    type: object
                                  quobyte:
                                    properties:
                                      group:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      registry:
                                        type: string
                                      tenant:
                                        type: string
                                      user:
                                        type: string
                                      volume:
                                        type: string
                                    required:
                                    - registry
                                    - volume
                                    type: object
                                  rbd:
                                    properties:
                                      fsType:
                                        type: string
                                      image:
                                        type: string
                                      keyring:
                                        type: string
                                      monitors:
                                        items:
                                          type: string
                                        type: array
                                      pool:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      user:
                                        type: string
                                    required:
                                    - image
                                    - monitors
                                    type: object
                                  scaleIO:
                                    properties:
                                      fsType:
                                        type: string
                                      gateway:
                                        type: string
                                      protectionDomain:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      sslEnabled:
                                        type: boolean
                                      storageMode:
                                        type: string
                                      storagePool:
                                        type: string
                                      system:
                                        type: string
                                      volumeName:
                                        type: string
                                    required:
                                    - gateway
                                    - secretRef
                                    - system
                                    type: object
                                  secret:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                          required:
                                          - key
                                          - path
                                          type: object
                                        type: array
                                      optional:
                                        type: boolean
                                      secretName:
                                        type: string
                                    type: object
                                  storageos:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      volumeName:
                                        type: string
                                      volumeNamespace:
                                        type: string
                                    type: object
                                  vsphereVolume:
                                    properties:
                                      fsType:
                                        type: string
                                      storagePolicyID:
                                        type: string
                                      storagePolicyName:
                                        type: string
                                      volumePath:
                                        type: string
                                    required:
                                    - volumePath
                                    type: object
                                required:
                                - name
                                type: object
                              type: array
                          required:
                          - containers
                          type: object
                      type: object
                    topologyPolicy:
                      type: string
                  type: object
                type: array
              ttlSecondsAfterFinished:
                format: int32
                type: integer
              volumes:
                items:
                  properties:
                    mountPath:
                      type: string
                    volumeClaim:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                        dataSource:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        dataSourceRef:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                            namespace:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        resources:
                          properties:
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                          type: object
                        selector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                        storageClassName:
                          type: string
                        volumeAttributesClassName:
                          type: string
                        volumeMode:
                          type: string
                        volumeName:
                          type: string
                      type: object
                    volumeClaimName:
                      type: string
                  required:
                  - mountPath
                  type: object
                type: array
            type: object
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    status:
                      type: string
                  required:
                  - status
                  type: object
                type: array
              controlledResources:
                additionalProperties:
                  type: string
                type: object
              failed:
                format: int32
                type: integer
              minAvailable:
                format: int32
                type: integer
              pending:
                format: int32
                type: integer
              retryCount:
                format: int32
                type: integer
              running:
                format: int32
                type: integer
              runningDuration:
                type: string
              state:
                properties:
                  lastTransitionTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  phase:
                    type: string
                  reason:
                    type: string
                type: object
              succeeded:
                format: int32
                type: integer
              taskStatusCount:
                additionalProperties:
                  properties:
                    phase:
                      additionalProperties:
                        format: int32
                        type: integer
                      type: object
                  type: object
                type: object
              terminating:
                format: int32
                type: integer
              unknown:
                format: int32
                type: integer
              version:
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: volcano/templates/bus_v1alpha1_command.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: commands.bus.volcano.sh
spec:
  group: bus.volcano.sh
  names:
    kind: Command
    listKind: CommandList
    plural: commands
    singular: command
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Command defines command structure.
        properties:
          action:
            description: Action defines the action that will be took to the target
              object.
            type: string
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          message:
            description: Human-readable message indicating details of this command.
            type: string
          metadata:
            type: object
          reason:
            description: Unique, one-word, CamelCase reason for this command.
            type: string
          target:
            description: TargetObject defines the target object of this command.
            properties:
              apiVersion:
                description: API version of the referent.
                type: string
              blockOwnerDeletion:
                description: If true, AND if the owner has the "foregroundDeletion"
                  finalizer, then the owner cannot be deleted from the key-value store
                  until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion
                  for how the garbage collector interacts with this field and enforces
                  the foreground deletion. Defaults to false. To set this field, a
                  user needs "delete" permission of the owner, otherwise 422 (Unprocessable
                  Entity) will be returned.
                type: boolean
              controller:
                description: If true, this reference points to the managing controller.
                type: boolean
              kind:
                description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
                type: string
              name:
                description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names'
                type: string
              uid:
                description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids'
                type: string
            required:
            - apiVersion
            - kind
            - name
            - uid
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: volcano/templates/controllers.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: volcano-controllers
  namespace: volcano-system
---
# Source: volcano/templates/controllers.yaml
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-controllers
rules:
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["create", "get", "list", "watch", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs"]
    verbs: ["create", "get", "list", "watch", "update", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs/status", "jobs/finalizers"]
    verbs: ["update", "patch"]
  - apiGroups: ["bus.volcano.sh"]
    resources: ["commands"]
    verbs: ["get", "list", "watch", "delete"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "list", "watch", "update", "patch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["create", "get", "list", "watch", "update", "bind", "delete", "patch"]
  - apiGroups: [""]
    resources: ["pods/finalizers"]
    verbs: ["update", "patch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "create", "delete", "update"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups", "queues", "queues/status"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: ["flow.volcano.sh"]
    resources: ["jobflows", "jobtemplates"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: [ "flow.volcano.sh" ]
    resources: [ "jobflows/status", "jobs/finalizers","jobtemplates/status", "jobtemplates/finalizers" ]
    verbs: [ "update", "patch" ]
  - apiGroups: ["scheduling.k8s.io"]
    resources: ["priorityclasses"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["networkpolicies"]
    verbs: ["get", "create", "delete"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "statefulsets"]
    verbs: ["get"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["get"]
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "create", "update", "watch"]
---
# Source: volcano/templates/controllers.yaml
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-controllers-role
subjects:
  - kind: ServiceAccount
    name: volcano-controllers
    namespace: volcano-system
roleRef:
  kind: ClusterRole
  name: volcano-controllers
  apiGroup: rbac.authorization.k8s.io
---
# Source: volcano/templates/controllers.yaml
kind: Deployment
apiVersion: apps/v1
metadata:
  name: volcano-controllers
  namespace: volcano-system
  labels:
    app: volcano-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: volcano-controller
  template:
    metadata:
      labels:
        app: volcano-controller
    spec:
      securityContext:
        seLinuxOptions:
          level: s0:c123,c456
        seccompProfile:
          type: RuntimeDefault
      serviceAccount: volcano-controllers
      priorityClassName: system-cluster-critical
      containers:
          - name: volcano-controllers
            image: volcanosh/vc-controller-manager:v1.9.1
            args:
              - --logtostderr
              - --enable-healthz=true
              - --leader-elect=false
              - -v=4
              - 2>&1
            imagePullPolicy: Always
            securityContext:
              allowPrivilegeEscalation: false
              capabilities:
                add:
                - DAC_OVERRIDE
                drop:
                - ALL
              runAsNonRoot: true
              runAsUser: 1000
---
# Source: volcano/templates/scheduler.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: volcano-scheduler
  namespace: volcano-system
---
# Source: volcano/templates/scheduler.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: volcano-scheduler-configmap
  namespace: volcano-system
data:
  volcano-scheduler.conf: |
    actions: "enqueue, allocate, backfill"
    tiers:
    - plugins:
      - name: priority
      - name: gang
        enablePreemptable: false
      - name: conformance
    - plugins:
      - name: overcommit
      - name: drf
        enablePreemptable: false
      - name: predicates
      - name: proportion
      - name: nodeorder
      - name: binpack
---
# Source: volcano/templates/scheduler.yaml
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-scheduler
rules:
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["create", "get", "list", "watch", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs"]
    verbs: ["get", "list", "watch", "update", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs/status"]
    verbs: ["update", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "list", "watch", "update", "patch"]
  - apiGroups: [""]
    resources: ["pods", "pods/status"]
    verbs: ["create", "get", "list", "watch", "update", "patch", "bind", "updateStatus", "delete"]
  - apiGroups: [""]
    resources: ["pods/binding"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["list", "watch", "update"]
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["list", "watch", "update"]
  - apiGroups: [""]
    resources: ["namespaces", "services", "replicationcontrollers"]
    verbs: ["list", "watch", "get"]
  - apiGroups: [""]
    resources: ["resourcequotas"]
    verbs: ["list", "watch"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get","list", "watch","update","patch"]
  - apiGroups: [ "storage.k8s.io" ]
    resources: ["storageclasses", "csinodes", "csidrivers", "csistoragecapacities"]
    verbs: [ "list", "watch" ]
  - apiGroups: ["policy"]
    resources: ["poddisruptionbudgets"]
    verbs: ["list", "watch"]
  - apiGroups: ["scheduling.k8s.io"]
    resources: ["priorityclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues/status"]
    verbs: ["update"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups"]
    verbs: ["list", "watch", "update"]
  - apiGroups: ["nodeinfo.volcano.sh"]
    resources: ["numatopologies"]
    verbs: ["get", "list", "watch", "delete"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "create", "delete", "update"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "replicasets", "statefulsets"]
    verbs: ["list", "watch", "get"]
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "create", "update", "watch"]
---
# Source: volcano/templates/scheduler.yaml
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: volcano-scheduler-role
subjects:
  - kind: ServiceAccount
    name: volcano-scheduler
    namespace: volcano-system
roleRef:
  kind: ClusterRole
  name: volcano-scheduler
  apiGroup: rbac.authorization.k8s.io
---
# Source: volcano/templates/scheduler.yaml
apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8080"
    prometheus.io/scrape: "true"
  name: volcano-scheduler-service
  namespace: volcano-system
  labels:
    app: volcano-scheduler
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 8080
    name: "metrics"
  selector:
    app: volcano-scheduler
  type: ClusterIP
---
# Source: volcano/templates/scheduler.yaml
kind: Deployment
apiVersion: apps/v1
metadata:
  name: volcano-scheduler
  namespace: volcano-system
  labels:
    app: volcano-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: volcano-scheduler
  template:
    metadata:
      labels:
        app: volcano-scheduler
    spec:
      securityContext:
        seLinuxOptions:
          level: s0:c123,c456
        seccompProfile:
          type: RuntimeDefault
      serviceAccount: volcano-scheduler
      priorityClassName: system-cluster-critical
      containers:
        - name: volcano-scheduler
          image: volcanosh/vc-scheduler:v1.9.1
          args:
            - --logtostderr
            - --scheduler-conf=/volcano.scheduler/volcano-scheduler.conf
            - --enable-healthz=true
            - --enable-metrics=true
            - --leader-elect=false
            - -v=3
            - 2>&1
          env:
            - name: DEBUG_SOCKET_DIR
              value: /tmp/klog-socks
          imagePullPolicy: Always
          volumeMounts:
            - name: scheduler-config
              mountPath: /volcano.scheduler
            - name: klog-sock
              mountPath: /tmp/klog-socks
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              add:
              - DAC_OVERRIDE
              drop:
              - ALL
            runAsNonRoot: true
            runAsUser: 1000
      volumes:
        - name: scheduler-config
          configMap:
            name: volcano-scheduler-configmap
        - name: klog-sock
          hostPath:
            path: /tmp/klog-socks
---
# Source: volcano/templates/scheduling_v1beta1_podgroup.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: podgroups.scheduling.volcano.sh
spec:
  group: scheduling.volcano.sh
  names:
    kind: PodGroup
    listKind: PodGroupList
    plural: podgroups
    shortNames:
    - pg
    - podgroup-v1beta1
    singular: podgroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: STATUS
      type: string
    - jsonPath: .spec.minMember
      name: minMember
      type: integer
    - jsonPath: .status.running
      name: RUNNINGS
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    - jsonPath: .spec.queue
      name: QUEUE
      priority: 1
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: PodGroup is a collection of Pod; used for batch workload.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: 'Specification of the desired behavior of the pod group.
              More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status'
            properties:
              minMember:
                description: MinMember defines the minimal number of members/tasks
                  to run the pod group; if there's not enough resources to start all
                  tasks, the scheduler will not start anyone.
                format: int32
                type: integer
              minResources:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: MinResources defines the minimal resource of members/tasks
                  to run the pod group; if there's not enough resources to start all
                  tasks, the scheduler will not start anyone.
                type: object
              minTaskMember:
                additionalProperties:
                  format: int32
                  type: integer
                description: MinTaskMember defines the minimal number of pods to run
                  each task in the pod group; if there's not enough resources to start
                  each task, the scheduler will not start anyone.
                type: object
              priorityClassName:
                description: If specified, indicates the PodGroup's priority. "system-node-critical"
                  and "system-cluster-critical" are two special keywords which indicate
                  the highest priorities with the former being the highest priority.
                  Any other name must be defined by creating a PriorityClass object
                  with that name. If not specified, the PodGroup priority will be
                  default or zero if there is no default.
                type: string
              queue:
                description: Queue defines the queue to allocate resource for PodGroup;
                  if queue does not exist, the PodGroup will not be scheduled. Defaults
                  to `default` Queue with the lowest weight.
                type: string
            type: object
          status:
            description: Status represents the current information about a pod group.
              This data may not be up to date.
            properties:
              conditions:
                description: The conditions of PodGroup.
                items:
                  description: PodGroupCondition contains details for the current
                    state of this pod group.
                  properties:
                    lastTransitionTime:
                      description: Last time the phase transitioned from another to
                        current phase.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the phase's
                        last transition.
                      type: string
                    status:
                      description: Status is the status of the condition.
                      type: string
                    transitionID:
                      description: The ID of condition transition.
                      type: string
                    type:
                      description: Type is the type of the condition
                      type: string
                  type: object
                type: array
              failed:
                description: The number of pods which reached phase Failed.
                format: int32
                type: integer
              phase:
                description: Current phase of PodGroup.
                type: string
              running:
                description: The number of actively running pods.
                format: int32
                type: integer
              succeeded:
                description: The number of pods which reached phase Succeeded.
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: volcano/templates/scheduling_v1beta1_queue.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: queues.scheduling.volcano.sh
spec:
  group: scheduling.volcano.sh
  names:
    kind: Queue
    listKind: QueueList
    plural: queues
    shortNames:
    - q
    - queue-v1beta1
    singular: queue
  scope: Cluster
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: Queue is a queue of PodGroup.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: 'Specification of the desired behavior of the queue. More
              info: https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status'
            properties:
              affinity:
                description: If specified, the pod owned by the queue will be scheduled
                  with constraint
                properties:
                  nodeGroupAffinity:
                    description: Describes nodegroup affinity scheduling rules for
                      the queue(e.g. putting pods of the queue in the nodes of the
                      nodegroup)
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          type: string
                        type: array
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          type: string
                        type: array
                    type: object
                  nodeGroupAntiAffinity:
                    description: Describes nodegroup anti-affinity scheduling rules
                      for the queue(e.g. avoid putting pods of the queue in the nodes
                      of the nodegroup).
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        items:
                          type: string
                        type: array
                      requiredDuringSchedulingIgnoredDuringExecution:
                        items:
                          type: string
                        type: array
                    type: object
                type: object
              capability:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: ResourceList is a set of (resource name, quantity) pairs.
                type: object
              deserved:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: The amount of resources configured by the user. This
                  part of resource can be shared with other queues and reclaimed back.
                type: object
              extendClusters:
                description: extendCluster indicate the jobs in this Queue will be
                  dispatched to these clusters.
                items:
                  description: CluterSpec represents the template of Cluster
                  properties:
                    capacity:
                      additionalProperties:
                        anyOf:
                        - type: integer
                        - type: string
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      description: ResourceList is a set of (resource name, quantity)
                        pairs.
                      type: object
                    name:
                      type: string
                    weight:
                      format: int32
                      type: integer
                  type: object
                type: array
              guarantee:
                description: Guarantee indicate configuration about resource reservation
                properties:
                  resource:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    description: The amount of cluster resource reserved for queue.
                      Just set either `percentage` or `resource`
                    type: object
                type: object
              parent:
                description: Parent define the parent of queue
                type: string
              reclaimable:
                description: Reclaimable indicate whether the queue can be reclaimed
                  by other queue
                type: boolean
              type:
                description: Type define the type of queue
                type: string
              weight:
                format: int32
                type: integer
            type: object
          status:
            description: The status of queue.
            properties:
              allocated:
                additionalProperties:
                  anyOf:
                  - type: integer
                  - type: string
                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                  x-kubernetes-int-or-string: true
                description: Allocated is allocated resources in queue
                type: object
              completed:
                description: The number of `Completed` PodGroup in this queue.
                format: int32
                type: integer
              inqueue:
                description: The number of `Inqueue` PodGroup in this queue.
                format: int32
                type: integer
              pending:
                description: The number of 'Pending' PodGroup in this queue.
                format: int32
                type: integer
              reservation:
                description: Reservation is the profile of resource reservation for
                  queue
                properties:
                  nodes:
                    description: Nodes are Locked nodes for queue
                    items:
                      type: string
                    type: array
                  resource:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    description: Resource is a list of total idle resource in locked
                      nodes.
                    type: object
                type: object
              running:
                description: The number of 'Running' PodGroup in this queue.
                format: int32
                type: integer
              state:
                description: State is state of queue
                type: string
              unknown:
                description: The number of 'Unknown' PodGroup in this queue.
                format: int32
                type: integer
            required:
            - allocated
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: volcano/templates/nodeinfo_v1alpha1_numatopologies.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: numatopologies.nodeinfo.volcano.sh
spec:
  group: nodeinfo.volcano.sh
  names:
    kind: Numatopology
    listKind: NumatopologyList
    plural: numatopologies
    shortNames:
    - numatopo
    singular: numatopology
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Numatopology is the Schema for the Numatopologies API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: Specification of the numa information of the worker node
            properties:
              cpuDetail:
                additionalProperties:
                  description: CPUInfo is the cpu topology detail
                  properties:
                    core:
                      type: integer
                    numa:
                      type: integer
                    socket:
                      type: integer
                  type: object
                description: Specifies the cpu topology info Key is cpu id
                type: object
              numares:
                additionalProperties:
                  description: ResourceInfo is the sets about resource capacity and
                    allocatable
                  properties:
                    allocatable:
                      type: string
                    capacity:
                      type: integer
                  type: object
                description: Specifies the numa info for the resource Key is resource
                  name
                type: object
              policies:
                additionalProperties:
                  type: string
                description: Specifies the policy of the manager
                type: object
              resReserved:
                additionalProperties:
                  type: string
                description: Specifies the reserved resource of the node Key is resource
                  name
                type: object
            type: object
        type: object
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: volcano-admission-service-pods-mutate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /pods/mutate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: mutatepod.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    reinvocationPolicy: Never
    rules:
      - apiGroups:
          - ""
        apiVersions:
          - v1
        operations:
          - CREATE
        resources:
          - pods
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: volcano-admission-service-queues-mutate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /queues/mutate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: mutatequeue.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    reinvocationPolicy: Never
    rules:
      - apiGroups:
          - scheduling.volcano.sh
        apiVersions:
          - v1beta1
        operations:
          - CREATE
        resources:
          - queues
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: volcano-admission-service-podgroups-mutate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /podgroups/mutate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: mutatepodgroup.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    reinvocationPolicy: Never
    rules:
      - apiGroups:
          - scheduling.volcano.sh
        apiVersions:
          - v1beta1
        operations:
          - CREATE
        resources:
          - podgroups
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: volcano-admission-service-jobs-mutate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /jobs/mutate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: mutatejob.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    reinvocationPolicy: Never
    rules:
      - apiGroups:
          - batch.volcano.sh
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
        resources:
          - jobs
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: volcano-admission-service-jobs-validate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /jobs/validate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: validatejob.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    rules:
      - apiGroups:
          - batch.volcano.sh
        apiVersions:
          - v1alpha1
        operations:
          - CREATE
          - UPDATE
        resources:
          - jobs
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: volcano-admission-service-pods-validate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /pods/validate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: validatepod.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    rules:
      - apiGroups:
          - ""
        apiVersions:
          - v1
        operations:
          - CREATE
        resources:
          - pods
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: volcano/templates/webhooks.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: volcano-admission-service-queues-validate
webhooks:
  - admissionReviewVersions:
      - v1
    clientConfig:
      service:
        name: volcano-admission-service
        namespace: volcano-system
        path: /queues/validate
        port: 443
    failurePolicy: Fail
    matchPolicy: Equivalent
    name: validatequeue.volcano.sh
    namespaceSelector:
      matchExpressions:
        - key: kubernetes.io/metadata.name
          operator: NotIn
          values:
            - volcano-system
            - kube-system
    objectSelector: {}
    rules:
      - apiGroups:
          - scheduling.volcano.sh
        apiVersions:
          - v1beta1
        operations:
          - CREATE
          - UPDATE
          - DELETE
        resources:
          - queues
        scope: '*'
    sideEffects: NoneOnDryRun
    timeoutSeconds: 10
---
# Source: jobflow/templates/flow_v1alpha1_jobflows.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: jobtemplates.flow.volcano.sh
spec:
  group: flow.volcano.sh
  names:
    kind: JobTemplate
    listKind: JobTemplateList
    plural: jobtemplates
    shortNames:
    - jt
    singular: jobtemplate
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              maxRetry:
                format: int32
                type: integer
              minAvailable:
                format: int32
                type: integer
              minSuccess:
                format: int32
                minimum: 1
                type: integer
              plugins:
                additionalProperties:
                  items:
                    type: string
                  type: array
                type: object
              policies:
                items:
                  properties:
                    action:
                      type: string
                    event:
                      type: string
                    events:
                      items:
                        type: string
                      type: array
                    exitCode:
                      format: int32
                      type: integer
                    timeout:
                      type: string
                  type: object
                type: array
              priorityClassName:
                type: string
              queue:
                type: string
              runningEstimate:
                type: string
              schedulerName:
                type: string
              tasks:
                items:
                  properties:
                    dependsOn:
                      properties:
                        iteration:
                          type: string
                        name:
                          items:
                            type: string
                          type: array
                      type: object
                    maxRetry:
                      format: int32
                      type: integer
                    minAvailable:
                      format: int32
                      type: integer
                    name:
                      type: string
                    policies:
                      items:
                        properties:
                          action:
                            type: string
                          event:
                            type: string
                          events:
                            items:
                              type: string
                            type: array
                          exitCode:
                            format: int32
                            type: integer
                          timeout:
                            type: string
                        type: object
                      type: array
                    replicas:
                      format: int32
                      type: integer
                    template:
                      properties:
                        metadata:
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              type: object
                            finalizers:
                              items:
                                type: string
                              type: array
                            labels:
                              additionalProperties:
                                type: string
                              type: object
                            name:
                              type: string
                            namespace:
                              type: string
                          type: object
                        spec:
                          properties:
                            activeDeadlineSeconds:
                              format: int64
                              type: integer
                            affinity:
                              properties:
                                nodeAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          preference:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchFields:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - preference
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      properties:
                                        nodeSelectorTerms:
                                          items:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchFields:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                            type: object
                                          type: array
                                      required:
                                      - nodeSelectorTerms
                                      type: object
                                  type: object
                                podAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          podAffinityTerm:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              matchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              mismatchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              namespaceSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              namespaces:
                                                items:
                                                  type: string
                                                type: array
                                              topologyKey:
                                                type: string
                                            required:
                                            - topologyKey
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - podAffinityTerm
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          labelSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          matchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          mismatchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          namespaceSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          namespaces:
                                            items:
                                              type: string
                                            type: array
                                          topologyKey:
                                            type: string
                                        required:
                                        - topologyKey
                                        type: object
                                      type: array
                                  type: object
                                podAntiAffinity:
                                  properties:
                                    preferredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          podAffinityTerm:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              matchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              mismatchLabelKeys:
                                                items:
                                                  type: string
                                                type: array
                                                x-kubernetes-list-type: atomic
                                              namespaceSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              namespaces:
                                                items:
                                                  type: string
                                                type: array
                                              topologyKey:
                                                type: string
                                            required:
                                            - topologyKey
                                            type: object
                                          weight:
                                            format: int32
                                            type: integer
                                        required:
                                        - podAffinityTerm
                                        - weight
                                        type: object
                                      type: array
                                    requiredDuringSchedulingIgnoredDuringExecution:
                                      items:
                                        properties:
                                          labelSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          matchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          mismatchLabelKeys:
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                          namespaceSelector:
                                            properties:
                                              matchExpressions:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    operator:
                                                      type: string
                                                    values:
                                                      items:
                                                        type: string
                                                      type: array
                                                  required:
                                                  - key
                                                  - operator
                                                  type: object
                                                type: array
                                              matchLabels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                            type: object
                                          namespaces:
                                            items:
                                              type: string
                                            type: array
                                          topologyKey:
                                            type: string
                                        required:
                                        - topologyKey
                                        type: object
                                      type: array
                                  type: object
                              type: object
                            automountServiceAccountToken:
                              type: boolean
                            containers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            dnsConfig:
                              properties:
                                nameservers:
                                  items:
                                    type: string
                                  type: array
                                options:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    type: object
                                  type: array
                                searches:
                                  items:
                                    type: string
                                  type: array
                              type: object
                            dnsPolicy:
                              type: string
                            enableServiceLinks:
                              type: boolean
                            ephemeralContainers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  targetContainerName:
                                    type: string
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            hostAliases:
                              items:
                                properties:
                                  hostnames:
                                    items:
                                      type: string
                                    type: array
                                  ip:
                                    type: string
                                type: object
                              type: array
                            hostIPC:
                              type: boolean
                            hostNetwork:
                              type: boolean
                            hostPID:
                              type: boolean
                            hostUsers:
                              type: boolean
                            hostname:
                              type: string
                            imagePullSecrets:
                              items:
                                properties:
                                  name:
                                    type: string
                                type: object
                              type: array
                            initContainers:
                              items:
                                properties:
                                  args:
                                    items:
                                      type: string
                                    type: array
                                  command:
                                    items:
                                      type: string
                                    type: array
                                  env:
                                    items:
                                      properties:
                                        name:
                                          type: string
                                        value:
                                          type: string
                                        valueFrom:
                                          properties:
                                            configMapKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                            secretKeyRef:
                                              properties:
                                                key:
                                                  type: string
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              required:
                                              - key
                                              type: object
                                          type: object
                                      required:
                                      - name
                                      type: object
                                    type: array
                                  envFrom:
                                    items:
                                      properties:
                                        configMapRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                        prefix:
                                          type: string
                                        secretRef:
                                          properties:
                                            name:
                                              type: string
                                            optional:
                                              type: boolean
                                          type: object
                                      type: object
                                    type: array
                                  image:
                                    type: string
                                  imagePullPolicy:
                                    type: string
                                  lifecycle:
                                    properties:
                                      postStart:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                      preStop:
                                        properties:
                                          exec:
                                            properties:
                                              command:
                                                items:
                                                  type: string
                                                type: array
                                            type: object
                                          httpGet:
                                            properties:
                                              host:
                                                type: string
                                              httpHeaders:
                                                items:
                                                  properties:
                                                    name:
                                                      type: string
                                                    value:
                                                      type: string
                                                  required:
                                                  - name
                                                  - value
                                                  type: object
                                                type: array
                                              path:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                              scheme:
                                                type: string
                                            required:
                                            - port
                                            type: object
                                          sleep:
                                            properties:
                                              seconds:
                                                format: int64
                                                type: integer
                                            required:
                                            - seconds
                                            type: object
                                          tcpSocket:
                                            properties:
                                              host:
                                                type: string
                                              port:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                x-kubernetes-int-or-string: true
                                            required:
                                            - port
                                            type: object
                                        type: object
                                    type: object
                                  livenessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  name:
                                    type: string
                                  ports:
                                    items:
                                      properties:
                                        containerPort:
                                          format: int32
                                          type: integer
                                        hostIP:
                                          type: string
                                        hostPort:
                                          format: int32
                                          type: integer
                                        name:
                                          type: string
                                        protocol:
                                          default: TCP
                                          type: string
                                      required:
                                      - containerPort
                                      type: object
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - containerPort
                                    - protocol
                                    x-kubernetes-list-type: map
                                  readinessProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  resizePolicy:
                                    items:
                                      properties:
                                        resourceName:
                                          type: string
                                        restartPolicy:
                                          type: string
                                      required:
                                      - resourceName
                                      - restartPolicy
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  resources:
                                    properties:
                                      claims:
                                        items:
                                          properties:
                                            name:
                                              type: string
                                          required:
                                          - name
                                          type: object
                                        type: array
                                        x-kubernetes-list-map-keys:
                                        - name
                                        x-kubernetes-list-type: map
                                      limits:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                      requests:
                                        additionalProperties:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                          x-kubernetes-int-or-string: true
                                        type: object
                                    type: object
                                  restartPolicy:
                                    type: string
                                  securityContext:
                                    properties:
                                      allowPrivilegeEscalation:
                                        type: boolean
                                      capabilities:
                                        properties:
                                          add:
                                            items:
                                              type: string
                                            type: array
                                          drop:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      privileged:
                                        type: boolean
                                      procMount:
                                        type: string
                                      readOnlyRootFilesystem:
                                        type: boolean
                                      runAsGroup:
                                        format: int64
                                        type: integer
                                      runAsNonRoot:
                                        type: boolean
                                      runAsUser:
                                        format: int64
                                        type: integer
                                      seLinuxOptions:
                                        properties:
                                          level:
                                            type: string
                                          role:
                                            type: string
                                          type:
                                            type: string
                                          user:
                                            type: string
                                        type: object
                                      seccompProfile:
                                        properties:
                                          localhostProfile:
                                            type: string
                                          type:
                                            type: string
                                        required:
                                        - type
                                        type: object
                                      windowsOptions:
                                        properties:
                                          gmsaCredentialSpec:
                                            type: string
                                          gmsaCredentialSpecName:
                                            type: string
                                          hostProcess:
                                            type: boolean
                                          runAsUserName:
                                            type: string
                                        type: object
                                    type: object
                                  startupProbe:
                                    properties:
                                      exec:
                                        properties:
                                          command:
                                            items:
                                              type: string
                                            type: array
                                        type: object
                                      failureThreshold:
                                        format: int32
                                        type: integer
                                      grpc:
                                        properties:
                                          port:
                                            format: int32
                                            type: integer
                                          service:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      httpGet:
                                        properties:
                                          host:
                                            type: string
                                          httpHeaders:
                                            items:
                                              properties:
                                                name:
                                                  type: string
                                                value:
                                                  type: string
                                              required:
                                              - name
                                              - value
                                              type: object
                                            type: array
                                          path:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                          scheme:
                                            type: string
                                        required:
                                        - port
                                        type: object
                                      initialDelaySeconds:
                                        format: int32
                                        type: integer
                                      periodSeconds:
                                        format: int32
                                        type: integer
                                      successThreshold:
                                        format: int32
                                        type: integer
                                      tcpSocket:
                                        properties:
                                          host:
                                            type: string
                                          port:
                                            anyOf:
                                            - type: integer
                                            - type: string
                                            x-kubernetes-int-or-string: true
                                        required:
                                        - port
                                        type: object
                                      terminationGracePeriodSeconds:
                                        format: int64
                                        type: integer
                                      timeoutSeconds:
                                        format: int32
                                        type: integer
                                    type: object
                                  stdin:
                                    type: boolean
                                  stdinOnce:
                                    type: boolean
                                  terminationMessagePath:
                                    type: string
                                  terminationMessagePolicy:
                                    type: string
                                  tty:
                                    type: boolean
                                  volumeDevices:
                                    items:
                                      properties:
                                        devicePath:
                                          type: string
                                        name:
                                          type: string
                                      required:
                                      - devicePath
                                      - name
                                      type: object
                                    type: array
                                  volumeMounts:
                                    items:
                                      properties:
                                        mountPath:
                                          type: string
                                        mountPropagation:
                                          type: string
                                        name:
                                          type: string
                                        readOnly:
                                          type: boolean
                                        subPath:
                                          type: string
                                        subPathExpr:
                                          type: string
                                      required:
                                      - mountPath
                                      - name
                                      type: object
                                    type: array
                                  workingDir:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                            nodeName:
                              type: string
                            nodeSelector:
                              additionalProperties:
                                type: string
                              type: object
                              x-kubernetes-map-type: atomic
                            os:
                              properties:
                                name:
                                  type: string
                              required:
                              - name
                              type: object
                            overhead:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            preemptionPolicy:
                              type: string
                            priority:
                              format: int32
                              type: integer
                            priorityClassName:
                              type: string
                            readinessGates:
                              items:
                                properties:
                                  conditionType:
                                    type: string
                                required:
                                - conditionType
                                type: object
                              type: array
                            resourceClaims:
                              items:
                                properties:
                                  name:
                                    type: string
                                  source:
                                    properties:
                                      resourceClaimName:
                                        type: string
                                      resourceClaimTemplateName:
                                        type: string
                                    type: object
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            restartPolicy:
                              type: string
                            runtimeClassName:
                              type: string
                            schedulerName:
                              type: string
                            schedulingGates:
                              items:
                                properties:
                                  name:
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            securityContext:
                              properties:
                                fsGroup:
                                  format: int64
                                  type: integer
                                fsGroupChangePolicy:
                                  type: string
                                runAsGroup:
                                  format: int64
                                  type: integer
                                runAsNonRoot:
                                  type: boolean
                                runAsUser:
                                  format: int64
                                  type: integer
                                seLinuxOptions:
                                  properties:
                                    level:
                                      type: string
                                    role:
                                      type: string
                                    type:
                                      type: string
                                    user:
                                      type: string
                                  type: object
                                seccompProfile:
                                  properties:
                                    localhostProfile:
                                      type: string
                                    type:
                                      type: string
                                  required:
                                  - type
                                  type: object
                                supplementalGroups:
                                  items:
                                    format: int64
                                    type: integer
                                  type: array
                                sysctls:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                windowsOptions:
                                  properties:
                                    gmsaCredentialSpec:
                                      type: string
                                    gmsaCredentialSpecName:
                                      type: string
                                    hostProcess:
                                      type: boolean
                                    runAsUserName:
                                      type: string
                                  type: object
                              type: object
                            serviceAccount:
                              type: string
                            serviceAccountName:
                              type: string
                            setHostnameAsFQDN:
                              type: boolean
                            shareProcessNamespace:
                              type: boolean
                            subdomain:
                              type: string
                            terminationGracePeriodSeconds:
                              format: int64
                              type: integer
                            tolerations:
                              items:
                                properties:
                                  effect:
                                    type: string
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  tolerationSeconds:
                                    format: int64
                                    type: integer
                                  value:
                                    type: string
                                type: object
                              type: array
                            topologySpreadConstraints:
                              items:
                                properties:
                                  labelSelector:
                                    properties:
                                      matchExpressions:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            operator:
                                              type: string
                                            values:
                                              items:
                                                type: string
                                              type: array
                                          required:
                                          - key
                                          - operator
                                          type: object
                                        type: array
                                      matchLabels:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    type: object
                                  matchLabelKeys:
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  maxSkew:
                                    format: int32
                                    type: integer
                                  minDomains:
                                    format: int32
                                    type: integer
                                  nodeAffinityPolicy:
                                    type: string
                                  nodeTaintsPolicy:
                                    type: string
                                  topologyKey:
                                    type: string
                                  whenUnsatisfiable:
                                    type: string
                                required:
                                - maxSkew
                                - topologyKey
                                - whenUnsatisfiable
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - topologyKey
                              - whenUnsatisfiable
                              x-kubernetes-list-type: map
                            volumes:
                              items:
                                properties:
                                  awsElasticBlockStore:
                                    properties:
                                      fsType:
                                        type: string
                                      partition:
                                        format: int32
                                        type: integer
                                      readOnly:
                                        type: boolean
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  azureDisk:
                                    properties:
                                      cachingMode:
                                        type: string
                                      diskName:
                                        type: string
                                      diskURI:
                                        type: string
                                      fsType:
                                        type: string
                                      kind:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - diskName
                                    - diskURI
                                    type: object
                                  azureFile:
                                    properties:
                                      readOnly:
                                        type: boolean
                                      secretName:
                                        type: string
                                      shareName:
                                        type: string
                                    required:
                                    - secretName
                                    - shareName
                                    type: object
                                  cephfs:
                                    properties:
                                      monitors:
                                        items:
                                          type: string
                                        type: array
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretFile:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      user:
                                        type: string
                                    required:
                                    - monitors
                                    type: object
                                  cinder:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  configMap:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                          required:
                                          - key
                                          - path
                                          type: object
                                        type: array
                                      name:
                                        type: string
                                      optional:
                                        type: boolean
                                    type: object
                                  csi:
                                    properties:
                                      driver:
                                        type: string
                                      fsType:
                                        type: string
                                      nodePublishSecretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      readOnly:
                                        type: boolean
                                      volumeAttributes:
                                        additionalProperties:
                                          type: string
                                        type: object
                                    required:
                                    - driver
                                    type: object
                                  downwardAPI:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            fieldRef:
                                              properties:
                                                apiVersion:
                                                  type: string
                                                fieldPath:
                                                  type: string
                                              required:
                                              - fieldPath
                                              type: object
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                            resourceFieldRef:
                                              properties:
                                                containerName:
                                                  type: string
                                                divisor:
                                                  anyOf:
                                                  - type: integer
                                                  - type: string
                                                  pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                  x-kubernetes-int-or-string: true
                                                resource:
                                                  type: string
                                              required:
                                              - resource
                                              type: object
                                          required:
                                          - path
                                          type: object
                                        type: array
                                    type: object
                                  emptyDir:
                                    properties:
                                      medium:
                                        type: string
                                      sizeLimit:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                    type: object
                                  ephemeral:
                                    properties:
                                      volumeClaimTemplate:
                                        properties:
                                          metadata:
                                            properties:
                                              annotations:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                              finalizers:
                                                items:
                                                  type: string
                                                type: array
                                              labels:
                                                additionalProperties:
                                                  type: string
                                                type: object
                                              name:
                                                type: string
                                              namespace:
                                                type: string
                                            type: object
                                          spec:
                                            properties:
                                              accessModes:
                                                items:
                                                  type: string
                                                type: array
                                              dataSource:
                                                properties:
                                                  apiGroup:
                                                    type: string
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                required:
                                                - kind
                                                - name
                                                type: object
                                              dataSourceRef:
                                                properties:
                                                  apiGroup:
                                                    type: string
                                                  kind:
                                                    type: string
                                                  name:
                                                    type: string
                                                  namespace:
                                                    type: string
                                                required:
                                                - kind
                                                - name
                                                type: object
                                              resources:
                                                properties:
                                                  limits:
                                                    additionalProperties:
                                                      anyOf:
                                                      - type: integer
                                                      - type: string
                                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                      x-kubernetes-int-or-string: true
                                                    type: object
                                                  requests:
                                                    additionalProperties:
                                                      anyOf:
                                                      - type: integer
                                                      - type: string
                                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                      x-kubernetes-int-or-string: true
                                                    type: object
                                                type: object
                                              selector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              storageClassName:
                                                type: string
                                              volumeAttributesClassName:
                                                type: string
                                              volumeMode:
                                                type: string
                                              volumeName:
                                                type: string
                                            type: object
                                        required:
                                        - spec
                                        type: object
                                    type: object
                                  fc:
                                    properties:
                                      fsType:
                                        type: string
                                      lun:
                                        format: int32
                                        type: integer
                                      readOnly:
                                        type: boolean
                                      targetWWNs:
                                        items:
                                          type: string
                                        type: array
                                      wwids:
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  flexVolume:
                                    properties:
                                      driver:
                                        type: string
                                      fsType:
                                        type: string
                                      options:
                                        additionalProperties:
                                          type: string
                                        type: object
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                    required:
                                    - driver
                                    type: object
                                  flocker:
                                    properties:
                                      datasetName:
                                        type: string
                                      datasetUUID:
                                        type: string
                                    type: object
                                  gcePersistentDisk:
                                    properties:
                                      fsType:
                                        type: string
                                      partition:
                                        format: int32
                                        type: integer
                                      pdName:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - pdName
                                    type: object
                                  gitRepo:
                                    properties:
                                      directory:
                                        type: string
                                      repository:
                                        type: string
                                      revision:
                                        type: string
                                    required:
                                    - repository
                                    type: object
                                  glusterfs:
                                    properties:
                                      endpoints:
                                        type: string
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - endpoints
                                    - path
                                    type: object
                                  hostPath:
                                    properties:
                                      path:
                                        type: string
                                      type:
                                        type: string
                                    required:
                                    - path
                                    type: object
                                  iscsi:
                                    properties:
                                      chapAuthDiscovery:
                                        type: boolean
                                      chapAuthSession:
                                        type: boolean
                                      fsType:
                                        type: string
                                      initiatorName:
                                        type: string
                                      iqn:
                                        type: string
                                      iscsiInterface:
                                        type: string
                                      lun:
                                        format: int32
                                        type: integer
                                      portals:
                                        items:
                                          type: string
                                        type: array
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      targetPortal:
                                        type: string
                                    required:
                                    - iqn
                                    - lun
                                    - targetPortal
                                    type: object
                                  name:
                                    type: string
                                  nfs:
                                    properties:
                                      path:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      server:
                                        type: string
                                    required:
                                    - path
                                    - server
                                    type: object
                                  persistentVolumeClaim:
                                    properties:
                                      claimName:
                                        type: string
                                      readOnly:
                                        type: boolean
                                    required:
                                    - claimName
                                    type: object
                                  photonPersistentDisk:
                                    properties:
                                      fsType:
                                        type: string
                                      pdID:
                                        type: string
                                    required:
                                    - pdID
                                    type: object
                                  portworxVolume:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      volumeID:
                                        type: string
                                    required:
                                    - volumeID
                                    type: object
                                  projected:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      sources:
                                        items:
                                          properties:
                                            clusterTrustBundle:
                                              properties:
                                                labelSelector:
                                                  properties:
                                                    matchExpressions:
                                                      items:
                                                        properties:
                                                          key:
                                                            type: string
                                                          operator:
                                                            type: string
                                                          values:
                                                            items:
                                                              type: string
                                                            type: array
                                                        required:
                                                        - key
                                                        - operator
                                                        type: object
                                                      type: array
                                                    matchLabels:
                                                      additionalProperties:
                                                        type: string
                                                      type: object
                                                  type: object
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                                path:
                                                  type: string
                                                signerName:
                                                  type: string
                                              required:
                                              - path
                                              type: object
                                            configMap:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                    required:
                                                    - key
                                                    - path
                                                    type: object
                                                  type: array
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              type: object
                                            downwardAPI:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      fieldRef:
                                                        properties:
                                                          apiVersion:
                                                            type: string
                                                          fieldPath:
                                                            type: string
                                                        required:
                                                        - fieldPath
                                                        type: object
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                      resourceFieldRef:
                                                        properties:
                                                          containerName:
                                                            type: string
                                                          divisor:
                                                            anyOf:
                                                            - type: integer
                                                            - type: string
                                                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                            x-kubernetes-int-or-string: true
                                                          resource:
                                                            type: string
                                                        required:
                                                        - resource
                                                        type: object
                                                    required:
                                                    - path
                                                    type: object
                                                  type: array
                                              type: object
                                            secret:
                                              properties:
                                                items:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      mode:
                                                        format: int32
                                                        type: integer
                                                      path:
                                                        type: string
                                                    required:
                                                    - key
                                                    - path
                                                    type: object
                                                  type: array
                                                name:
                                                  type: string
                                                optional:
                                                  type: boolean
                                              type: object
                                            serviceAccountToken:
                                              properties:
                                                audience:
                                                  type: string
                                                expirationSeconds:
                                                  format: int64
                                                  type: integer
                                                path:
                                                  type: string
                                              required:
                                              - path
                                              type: object
                                          type: object
                                        type: array
                                    type: object
                                  quobyte:
                                    properties:
                                      group:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      registry:
                                        type: string
                                      tenant:
                                        type: string
                                      user:
                                        type: string
                                      volume:
                                        type: string
                                    required:
                                    - registry
                                    - volume
                                    type: object
                                  rbd:
                                    properties:
                                      fsType:
                                        type: string
                                      image:
                                        type: string
                                      keyring:
                                        type: string
                                      monitors:
                                        items:
                                          type: string
                                        type: array
                                      pool:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      user:
                                        type: string
                                    required:
                                    - image
                                    - monitors
                                    type: object
                                  scaleIO:
                                    properties:
                                      fsType:
                                        type: string
                                      gateway:
                                        type: string
                                      protectionDomain:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      sslEnabled:
                                        type: boolean
                                      storageMode:
                                        type: string
                                      storagePool:
                                        type: string
                                      system:
                                        type: string
                                      volumeName:
                                        type: string
                                    required:
                                    - gateway
                                    - secretRef
                                    - system
                                    type: object
                                  secret:
                                    properties:
                                      defaultMode:
                                        format: int32
                                        type: integer
                                      items:
                                        items:
                                          properties:
                                            key:
                                              type: string
                                            mode:
                                              format: int32
                                              type: integer
                                            path:
                                              type: string
                                          required:
                                          - key
                                          - path
                                          type: object
                                        type: array
                                      optional:
                                        type: boolean
                                      secretName:
                                        type: string
                                    type: object
                                  storageos:
                                    properties:
                                      fsType:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                        type: object
                                      volumeName:
                                        type: string
                                      volumeNamespace:
                                        type: string
                                    type: object
                                  vsphereVolume:
                                    properties:
                                      fsType:
                                        type: string
                                      storagePolicyID:
                                        type: string
                                      storagePolicyName:
                                        type: string
                                      volumePath:
                                        type: string
                                    required:
                                    - volumePath
                                    type: object
                                required:
                                - name
                                type: object
                              type: array
                          required:
                          - containers
                          type: object
                      type: object
                    topologyPolicy:
                      type: string
                  type: object
                type: array
              ttlSecondsAfterFinished:
                format: int32
                type: integer
              volumes:
                items:
                  properties:
                    mountPath:
                      type: string
                    volumeClaim:
                      properties:
                        accessModes:
                          items:
                            type: string
                          type: array
                        dataSource:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        dataSourceRef:
                          properties:
                            apiGroup:
                              type: string
                            kind:
                              type: string
                            name:
                              type: string
                            namespace:
                              type: string
                          required:
                          - kind
                          - name
                          type: object
                        resources:
                          properties:
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              type: object
                          type: object
                        selector:
                          properties:
                            matchExpressions:
                              items:
                                properties:
                                  key:
                                    type: string
                                  operator:
                                    type: string
                                  values:
                                    items:
                                      type: string
                                    type: array
                                required:
                                - key
                                - operator
                                type: object
                              type: array
                            matchLabels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                        storageClassName:
                          type: string
                        volumeAttributesClassName:
                          type: string
                        volumeMode:
                          type: string
                        volumeName:
                          type: string
                      type: object
                    volumeClaimName:
                      type: string
                  required:
                  - mountPath
                  type: object
                type: array
            type: object
          status:
            properties:
              jobDependsOnList:
                items:
                  type: string
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
---
# Source: jobflow/templates/flow_v1alpha1_jobtemplates.yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: jobflows.flow.volcano.sh
spec:
  group: flow.volcano.sh
  names:
    kind: JobFlow
    listKind: JobFlowList
    plural: jobflows
    shortNames:
    - jf
    singular: jobflow
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: Status
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              flows:
                items:
                  properties:
                    dependsOn:
                      properties:
                        probe:
                          properties:
                            httpGetList:
                              items:
                                properties:
                                  httpHeader:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  path:
                                    type: string
                                  port:
                                    type: integer
                                  taskName:
                                    type: string
                                type: object
                              type: array
                            taskStatusList:
                              items:
                                properties:
                                  phase:
                                    type: string
                                  taskName:
                                    type: string
                                type: object
                              type: array
                            tcpSocketList:
                              items:
                                properties:
                                  port:
                                    type: integer
                                  taskName:
                                    type: string
                                required:
                                - port
                                type: object
                              type: array
                          type: object
                        targets:
                          items:
                            type: string
                          type: array
                      type: object
                    name:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              jobRetainPolicy:
                type: string
            type: object
          status:
            properties:
              completedJobs:
                items:
                  type: string
                type: array
              conditions:
                additionalProperties:
                  properties:
                    createTime:
                      format: date-time
                      type: string
                    phase:
                      type: string
                    runningDuration:
                      type: string
                    taskStatusCount:
                      additionalProperties:
                        properties:
                          phase:
                            additionalProperties:
                              format: int32
                              type: integer
                            type: object
                        type: object
                      type: object
                  type: object
                type: object
              failedJobs:
                items:
                  type: string
                type: array
              jobStatusList:
                items:
                  properties:
                    endTimestamp:
                      format: date-time
                      type: string
                    name:
                      type: string
                    restartCount:
                      format: int32
                      type: integer
                    runningHistories:
                      items:
                        properties:
                          endTimestamp:
                            format: date-time
                            type: string
                          startTimestamp:
                            format: date-time
                            type: string
                          state:
                            type: string
                        type: object
                      type: array
                    startTimestamp:
                      format: date-time
                      type: string
                    state:
                      type: string
                  type: object
                type: array
              pendingJobs:
                items:
                  type: string
                type: array
              runningJobs:
                items:
                  type: string
                type: array
              state:
                properties:
                  phase:
                    type: string
                type: object
              terminatedJobs:
                items:
                  type: string
                type: array
              unKnowJobs:
                items:
                  type: string
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
