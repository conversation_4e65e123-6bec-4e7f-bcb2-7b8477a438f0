#resourceGroups:
#- resourceGroup: management                    # set the resource group name
#  object:
#    key: namespace                             # set the field and the value to be matched
#    value:
#    - mng-ns-1
#  schedulerName: default-scheduler             # set the scheduler for patching
#  tolerations:                                 # set the tolerations for patching
#  - effect: NoSchedule
#    key: taint
#    operator: Exists
#  labels:
#    volcano.sh/nodetype: management           # set the nodeSelector for patching
#- resourceGroup: cpu
#  object:
#    key: annotation
#    value:
#    - "volcano.sh/resource-group: cpu"
#  schedulerName: volcano
#  labels:
#    volcano.sh/nodetype: cpu
#- resourceGroup: gpu                          # if the object is unsetted, default is:  the key is annotation,
#  schedulerName: volcano                      # the annotation key is fixed and is "volcano.sh/resource-group", The corresponding value is the resourceGroup field
#  labels:
#    volcano.sh/nodetype: gpu
