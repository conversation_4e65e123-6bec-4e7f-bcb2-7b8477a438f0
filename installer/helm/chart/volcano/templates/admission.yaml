{{- if .Values.custom.admission_enable }}
{{ $admission_affinity := or .Values.custom.admission_affinity .Values.custom.default_affinity }}
{{ $admission_tolerations := or .Values.custom.admission_tolerations .Values.custom.default_tolerations }}
{{ $admission_sc := or .Values.custom.admission_sc .Values.custom.default_sc }}
{{ $admission_main_csc := or .Values.custom.admission_main_csc .Values.custom.default_csc }}
{{ $admission_init_csc := or .Values.custom.admission_init_csc .Values.custom.default_csc }}
{{ $admission_ns := or .Values.custom.admission_ns .Values.custom.default_ns }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-admission-configmap
  namespace: {{ .Release.Namespace }}
data:
  {{- (.Files.Glob .Values.basic.admission_config_file).AsConfig | nindent 2}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-admission
  namespace: {{ .Release.Namespace }}
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-admission
rules:
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations", "validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "create", "update"]
  # Rules below is used generate admission service secret
  - apiGroups: ["certificates.k8s.io"]
    resources: ["certificatesigningrequests"]
    verbs: ["get", "list", "create", "delete"]
  - apiGroups: ["certificates.k8s.io"]
    resources: ["certificatesigningrequests/approval"]
    verbs: ["create", "update"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create", "get", "patch"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues"]
    verbs: ["get", "list"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups"]
    verbs: ["get", "list", "watch"]

---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-admission-role
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-admission
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ .Release.Name }}-admission
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: volcano-admission
    {{- if .Values.custom.admission_labels }}
    {{- toYaml .Values.custom.admission_labels | nindent 4 }}
    {{- end }}
  name: {{ .Release.Name }}-admission
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{ .Values.custom.admission_replicas }}
  selector:
    matchLabels:
      app: volcano-admission
  template:
    metadata:
      labels:
        app: volcano-admission
        {{- if .Values.custom.admission_podLabels }}
        {{- toYaml .Values.custom.admission_podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- if $admission_tolerations }}
      tolerations: {{- toYaml $admission_tolerations | nindent 8 }}
      {{- end }}
      {{- if $admission_ns }}
      nodeSelector: {{- toYaml $admission_ns | nindent 8 }}
      {{- end }}
      {{- if $admission_affinity }}
      affinity:
          {{- toYaml $admission_affinity | nindent 8 }}
      {{- end }}
      {{- if $admission_sc }}
      securityContext:
          {{- toYaml $admission_sc | nindent 8 }}
      {{- end }}
      serviceAccount: {{ .Release.Name }}-admission
      priorityClassName: system-cluster-critical
      {{- if .Values.basic.image_pull_secret }}
      imagePullSecrets:
        - name: {{ .Values.basic.image_pull_secret }}
      {{- end }}
      containers:
        - args:
            - --enabled-admission={{ .Values.custom.enabled_admissions }}
            - --tls-cert-file=/admission.local.config/certificates/tls.crt
            - --tls-private-key-file=/admission.local.config/certificates/tls.key
            - --ca-cert-file=/admission.local.config/certificates/ca.crt
            - --admission-conf=/admission.local.config/configmap/{{base .Values.basic.admission_config_file}}
            - --webhook-namespace={{ .Release.Namespace }}
            - --webhook-service-name={{ .Release.Name }}-admission-service
            - --enable-healthz=true
            - --logtostderr
            - --port={{.Values.basic.admission_port}}
            - -v=4
            - 2>&1
          image: {{.Values.basic.admission_image_name}}:{{.Values.basic.image_tag_version}}
          imagePullPolicy: {{ .Values.basic.image_pull_policy }}
          name: admission
          {{- if .Values.custom.admission_resources }}
          resources:
          {{- toYaml .Values.custom.admission_resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - mountPath: /admission.local.config/certificates
              name: admission-certs
              readOnly: true
            - mountPath: /admission.local.config/configmap
              name: admission-config
          {{- if $admission_main_csc }}
          securityContext:
            {{- toYaml $admission_main_csc | nindent 12 }}
          {{- end }}
      volumes:
        - name: admission-certs
          secret:
            defaultMode: 420
            secretName: {{.Values.basic.admission_secret_name}}
        - name: admission-config
          configMap:
            name: {{ .Release.Name }}-admission-configmap

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: volcano-admission
  name: {{ .Release.Name }}-admission-service
  namespace: {{ .Release.Namespace }}
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: {{.Values.basic.admission_port}}
  selector:
    app: volcano-admission
  sessionAffinity: None

---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-admission-init
  namespace: {{ .Release.Namespace }}
  labels:
    app: volcano-admission-init
    {{- if .Values.custom.admission_labels }}
    {{- toYaml .Values.custom.admission_labels | nindent 4 }}
    {{- end }}
spec:
  backoffLimit: 3
  template:
    spec:
      {{- if $admission_tolerations }}
      tolerations: {{- toYaml $admission_tolerations | nindent 8 }}
      {{- end }}
      {{- if $admission_ns }}
      nodeSelector: {{- toYaml $admission_ns | nindent 8 }}
      {{- end }}
      {{- if $admission_affinity }}
      affinity:
        {{- toYaml $admission_affinity | nindent 8 }}
      {{- end }}
      {{- if $admission_sc }}
      securityContext:
        {{- toYaml $admission_sc | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ .Release.Name }}-admission
      priorityClassName: system-cluster-critical
      {{- if .Values.basic.image_pull_secret }}
      imagePullSecrets:
        - name: {{ .Values.basic.image_pull_secret }}
      {{- end }}
      restartPolicy: Never
      containers:
        - name: main
          {{- if .Values.custom.admission_resources }}
          resources:
          {{- toYaml .Values.custom.admission_resources | nindent 12 }}
          {{- end }}
          image: {{.Values.basic.admission_image_name}}:{{.Values.basic.image_tag_version}}
          imagePullPolicy: {{ .Values.basic.image_pull_policy }}
          command: ["./gen-admission-secret.sh", "--service", "{{ .Release.Name }}-admission-service", "--namespace",
                    "{{ .Release.Namespace }}", "--secret", "{{.Values.basic.admission_secret_name}}"]
          {{- if $admission_init_csc }}
          securityContext:
            {{- toYaml $admission_init_csc | nindent 12 }}
          {{- end }}
{{- end }}
