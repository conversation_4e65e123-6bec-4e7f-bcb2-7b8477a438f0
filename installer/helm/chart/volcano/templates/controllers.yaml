{{- if .Values.custom.controller_enable }}
{{ $controller_affinity := or .Values.custom.controller_affinity .Values.custom.default_affinity }}
{{ $controller_tolerations := or .Values.custom.controller_tolerations .Values.custom.default_tolerations }}
{{ $controller_sc := or .Values.custom.controller_sc .Values.custom.default_sc }}
{{ $controller_main_csc := or .Values.custom.controller_main_csc .Values.custom.default_csc }}
{{ $controller_ns := or .Values.custom.controller_ns .Values.custom.default_ns }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-controllers
  namespace: {{ .Release.Namespace }}

---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-controllers
rules:
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["create", "get", "list", "watch", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs"]
    verbs: ["create", "get", "list", "watch", "update", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs/status", "jobs/finalizers"]
    verbs: ["update", "patch"]
  - apiGroups: ["bus.volcano.sh"]
    resources: ["commands"]
    verbs: ["get", "list", "watch", "delete"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "list", "watch", "update", "patch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["create", "get", "list", "watch", "update", "bind", "delete", "patch"]
  - apiGroups: [""]
    resources: ["pods/finalizers"]
    verbs: ["update", "patch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create"]
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "create", "delete", "update"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups", "queues", "queues/status"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: ["flow.volcano.sh"]
    resources: ["jobflows", "jobtemplates"]
    verbs: ["get", "list", "watch", "create", "delete", "update"]
  - apiGroups: [ "flow.volcano.sh" ]
    resources: [ "jobflows/status", "jobs/finalizers","jobtemplates/status", "jobtemplates/finalizers" ]
    verbs: [ "update", "patch" ]
  - apiGroups: ["scheduling.k8s.io"]
    resources: ["priorityclasses"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["networkpolicies"]
    verbs: ["get", "create", "delete"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "statefulsets"]
    verbs: ["get"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["get"]
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "create", "update", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-controllers-role
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-controllers
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ .Release.Name }}-controllers
  apiGroup: rbac.authorization.k8s.io

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .Release.Name }}-controllers
  namespace: {{ .Release.Namespace }}
  labels:
    app: volcano-controller
    {{- if .Values.custom.controller_labels }}
    {{- toYaml .Values.custom.controller_labels | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.custom.controller_replicas }}
  selector:
    matchLabels:
      app: volcano-controller
  template:
    metadata:
      labels:
        app: volcano-controller
        {{- if .Values.custom.controller_podLabels }}
        {{- toYaml .Values.custom.controller_podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- if $controller_tolerations }}
      tolerations: {{- toYaml $controller_tolerations | nindent 8 }}
      {{- end }}
      {{- if $controller_ns }}
      nodeSelector: {{- toYaml $controller_ns | nindent 8 }}
      {{- end }}
      {{- if $controller_affinity }}
      affinity:
        {{- toYaml $controller_affinity | nindent 8 }}
      {{- end }}
      {{- if $controller_sc }}
      securityContext:
        {{- toYaml $controller_sc | nindent 8 }}
      {{- end }}
      serviceAccount: {{ .Release.Name }}-controllers
      priorityClassName: system-cluster-critical
      {{- if .Values.basic.image_pull_secret }}
      imagePullSecrets:
          - name: {{ .Values.basic.image_pull_secret }}
      {{- end }}
      containers:
          - name: {{ .Release.Name }}-controllers
            {{- if .Values.custom.controller_resources }}
            resources:
            {{- toYaml .Values.custom.controller_resources | nindent 14 }}
            {{- end }}
            image: {{.Values.basic.controller_image_name}}:{{.Values.basic.image_tag_version}}
            args:
              - --logtostderr
              - --enable-healthz=true
              - --leader-elect={{ .Values.custom.leader_elect_enable }}
              {{- if .Values.custom.leader_elect_enable }}
              - --lock-object-namespace={{ .Release.Namespace }}
              {{- end }}
              - -v=4
              - 2>&1
            imagePullPolicy: {{ .Values.basic.image_pull_policy }}
            {{- if $controller_main_csc }}
            securityContext:
              {{- toYaml $controller_main_csc | nindent 14 }}
            {{- end }}
{{- end }}
