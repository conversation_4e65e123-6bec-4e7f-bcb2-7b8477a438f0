{{- if .Values.custom.scheduler_enable }}
{{ $scheduler_affinity := or .Values.custom.scheduler_affinity .Values.custom.default_affinity }}
{{ $scheduler_tolerations := or .Values.custom.scheduler_tolerations .Values.custom.default_tolerations }}
{{ $scheduler_sc := or .Values.custom.scheduler_sc .Values.custom.default_sc }}
{{ $scheduler_main_csc := or .Values.custom.scheduler_main_csc .Values.custom.default_csc }}
{{ $scheduler_ns := or .Values.custom.scheduler_ns .Values.custom.default_ns }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-scheduler-configmap
  namespace: {{ .Release.Namespace }}
data:
  {{- (.Files.Glob .Values.basic.scheduler_config_file).AsConfig | nindent 2}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-scheduler
  namespace: {{ .Release.Namespace }}
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-scheduler
rules:
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["create", "get", "list", "watch", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs"]
    verbs: ["get", "list", "watch", "update", "delete"]
  - apiGroups: ["batch.volcano.sh"]
    resources: ["jobs/status"]
    verbs: ["update", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "list", "watch", "update", "patch"]
  - apiGroups: [""]
    resources: ["pods", "pods/status"]
    verbs: ["create", "get", "list", "watch", "update", "patch", "bind", "updateStatus", "delete"]
  - apiGroups: [""]
    resources: ["pods/binding"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["list", "watch", "update"]
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["list", "watch", "update"]
  - apiGroups: [""]
    resources: ["namespaces", "services", "replicationcontrollers"]
    verbs: ["list", "watch", "get"]
  - apiGroups: [""]
    resources: ["resourcequotas"]
    verbs: ["list", "watch"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get","list", "watch","update","patch"]
  - apiGroups: [ "storage.k8s.io" ]
    resources: ["storageclasses", "csinodes", "csidrivers", "csistoragecapacities"]
    verbs: [ "list", "watch" ]
  - apiGroups: ["policy"]
    resources: ["poddisruptionbudgets"]
    verbs: ["list", "watch"]
  - apiGroups: ["scheduling.k8s.io"]
    resources: ["priorityclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues"]
    verbs: ["get", "list", "watch", "create", "delete"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["queues/status"]
    verbs: ["update"]
  - apiGroups: ["scheduling.incubator.k8s.io", "scheduling.volcano.sh"]
    resources: ["podgroups"]
    verbs: ["list", "watch", "update"]
  - apiGroups: ["nodeinfo.volcano.sh"]
    resources: ["numatopologies"]
    verbs: ["get", "list", "watch", "delete"]
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "create", "delete", "update"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "replicasets", "statefulsets"]
    verbs: ["list", "watch", "get"]
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "create", "update", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .Release.Name }}-scheduler-role
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-scheduler
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{ .Release.Name }}-scheduler
  apiGroup: rbac.authorization.k8s.io

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .Release.Name }}-scheduler
  namespace: {{ .Release.Namespace }}
  labels:
    app: volcano-scheduler
    {{- if .Values.custom.scheduler_labels }}
    {{- toYaml .Values.custom.scheduler_labels | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.custom.scheduler_replicas }}
  selector:
    matchLabels:
      app: volcano-scheduler
  template:
    metadata:
      labels:
        app: volcano-scheduler
        {{- if .Values.custom.scheduler_podLabels }}
        {{- toYaml .Values.custom.scheduler_podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- if $scheduler_tolerations }}
      tolerations: {{- toYaml $scheduler_tolerations | nindent 8 }}
      {{- end }}
      {{- if $scheduler_ns }}
      nodeSelector: {{- toYaml $scheduler_ns | nindent 8 }}
      {{- end }}
      {{- if $scheduler_affinity }}
      affinity:
        {{- toYaml $scheduler_affinity | nindent 8 }}
      {{- end }}
      {{- if $scheduler_sc }}
      securityContext:
        {{- toYaml $scheduler_sc | nindent 8 }}
      {{- end }}
      serviceAccount: {{ .Release.Name }}-scheduler
      priorityClassName: system-cluster-critical
      {{- if .Values.basic.image_pull_secret }}
      imagePullSecrets:
          - name: {{ .Values.basic.image_pull_secret }}
      {{- end }}
      containers:
        - name: {{ .Release.Name }}-scheduler
          image: {{.Values.basic.scheduler_image_name}}:{{.Values.basic.image_tag_version}}
          {{- if .Values.custom.scheduler_resources }}
          resources:
          {{- toYaml .Values.custom.scheduler_resources | nindent 12 }}
          {{- end }}
          args:
            - --logtostderr
            - --scheduler-conf=/volcano.scheduler/{{base .Values.basic.scheduler_config_file}}
            - --enable-healthz=true
            - --enable-metrics=true
            {{- if .Values.custom.scheduler_pprof_enable }}
            - --enable-pprof=true
            {{- end }}
            - --leader-elect={{ .Values.custom.leader_elect_enable }}
            {{- if .Values.custom.leader_elect_enable }}
            - --lock-object-namespace={{ .Release.Namespace }}
            {{- end }}
            - -v=3
            - 2>&1
          env:
            - name: DEBUG_SOCKET_DIR
              value: /tmp/klog-socks
          imagePullPolicy: {{ .Values.basic.image_pull_policy }}
          volumeMounts:
            - name: scheduler-config
              mountPath: /volcano.scheduler
            - name: klog-sock
              mountPath: /tmp/klog-socks
          {{- if $scheduler_main_csc }}
          securityContext:
            {{- toYaml $scheduler_main_csc | nindent 12 }}
          {{- end }}
      volumes:
        - name: scheduler-config
          configMap:
            name: {{ .Release.Name }}-scheduler-configmap
        - name: klog-sock
          hostPath:
            path: /tmp/klog-socks
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8080"
    prometheus.io/scrape: "true"
  name: {{ .Release.Name }}-scheduler-service
  namespace: {{ .Release.Namespace }}
  labels:
    app: volcano-scheduler
spec:
  ports:
  - port: 8080
    protocol: TCP
    targetPort: 8080
    name: "metrics"
  selector:
    app: volcano-scheduler
  type: ClusterIP
{{- end }}
