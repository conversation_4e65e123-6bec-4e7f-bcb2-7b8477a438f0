apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: queues.scheduling.volcano.sh
spec:
  group: scheduling.volcano.sh
  names:
    kind: Queue
    listKind: QueueList
    plural: queues
    shortNames:
    - q
    - queue-v1beta1
    singular: queue
  scope: Cluster
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      description: Queue is a queue of PodGroup.
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        metadata:
          type: object
        spec:
          description: 'Specification of the desired behavior of the queue. More info:
            https://git.k8s.io/community/contributors/devel/api-conventions.md#spec-and-status'
          properties:
            affinity:
              description: If specified, the pod owned by the queue will be scheduled
                with constraint
              properties:
                nodeGroupAffinity:
                  description: Describes nodegroup affinity scheduling rules for the
                    queue(e.g. putting pods of the queue in the nodes of the nodegroup)
                  properties:
                    preferredDuringSchedulingIgnoredDuringExecution:
                      items:
                        type: string
                      type: array
                    requiredDuringSchedulingIgnoredDuringExecution:
                      items:
                        type: string
                      type: array
                  type: object
                nodeGroupAntiAffinity:
                  description: Describes nodegroup anti-affinity scheduling rules
                    for the queue(e.g. avoid putting pods of the queue in the nodes
                    of the nodegroup).
                  properties:
                    preferredDuringSchedulingIgnoredDuringExecution:
                      items:
                        type: string
                      type: array
                    requiredDuringSchedulingIgnoredDuringExecution:
                      items:
                        type: string
                      type: array
                  type: object
              type: object
            capability:
              additionalProperties:
                anyOf:
                - type: integer
                - type: string
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              description: ResourceList is a set of (resource name, quantity) pairs.
              type: object
            deserved:
              additionalProperties:
                anyOf:
                - type: integer
                - type: string
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              description: The amount of resources configured by the user. This part
                of resource can be shared with other queues and reclaimed back.
              type: object
            extendClusters:
              description: extendCluster indicate the jobs in this Queue will be dispatched
                to these clusters.
              items:
                description: CluterSpec represents the template of Cluster
                properties:
                  capacity:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    description: ResourceList is a set of (resource name, quantity)
                      pairs.
                    type: object
                  name:
                    type: string
                  weight:
                    format: int32
                    type: integer
                type: object
              type: array
            guarantee:
              description: Guarantee indicate configuration about resource reservation
              properties:
                resource:
                  additionalProperties:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  description: The amount of cluster resource reserved for queue.
                    Just set either `percentage` or `resource`
                  type: object
              type: object
            parent:
              description: Parent define the parent of queue
              type: string
            reclaimable:
              description: Reclaimable indicate whether the queue can be reclaimed
                by other queue
              type: boolean
            type:
              description: Type define the type of queue
              type: string
            weight:
              format: int32
              type: integer
          type: object
        status:
          description: The status of queue.
          properties:
            allocated:
              additionalProperties:
                anyOf:
                - type: integer
                - type: string
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              description: Allocated is allocated resources in queue
              type: object
            completed:
              description: The number of `Completed` PodGroup in this queue.
              format: int32
              type: integer
            inqueue:
              description: The number of `Inqueue` PodGroup in this queue.
              format: int32
              type: integer
            pending:
              description: The number of 'Pending' PodGroup in this queue.
              format: int32
              type: integer
            reservation:
              description: Reservation is the profile of resource reservation for
                queue
              properties:
                nodes:
                  description: Nodes are Locked nodes for queue
                  items:
                    type: string
                  type: array
                resource:
                  additionalProperties:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  description: Resource is a list of total idle resource in locked
                    nodes.
                  type: object
              type: object
            running:
              description: The number of 'Running' PodGroup in this queue.
              format: int32
              type: integer
            state:
              description: State is state of queue
              type: string
            unknown:
              description: The number of 'Unknown' PodGroup in this queue.
              format: int32
              type: integer
          required:
          - allocated
          type: object
      type: object
  version: v1beta1
  versions:
  - name: v1beta1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
