apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: jobflows.flow.volcano.sh
spec:
  group: flow.volcano.sh
  names:
    kind: JobFlow
    listKind: JobFlowList
    plural: jobflows
    shortNames:
    - jf
    singular: jobflow
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.state.phase
      name: Status
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              flows:
                items:
                  properties:
                    dependsOn:
                      properties:
                        probe:
                          properties:
                            httpGetList:
                              items:
                                properties:
                                  httpHeader:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  path:
                                    type: string
                                  port:
                                    type: integer
                                  taskName:
                                    type: string
                                type: object
                              type: array
                            taskStatusList:
                              items:
                                properties:
                                  phase:
                                    type: string
                                  taskName:
                                    type: string
                                type: object
                              type: array
                            tcpSocketList:
                              items:
                                properties:
                                  port:
                                    type: integer
                                  taskName:
                                    type: string
                                required:
                                - port
                                type: object
                              type: array
                          type: object
                        targets:
                          items:
                            type: string
                          type: array
                      type: object
                    name:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              jobRetainPolicy:
                type: string
            type: object
          status:
            properties:
              completedJobs:
                items:
                  type: string
                type: array
              conditions:
                additionalProperties:
                  properties:
                    createTime:
                      format: date-time
                      type: string
                    phase:
                      type: string
                    runningDuration:
                      type: string
                    taskStatusCount:
                      additionalProperties:
                        properties:
                          phase:
                            additionalProperties:
                              format: int32
                              type: integer
                            type: object
                        type: object
                      type: object
                  type: object
                type: object
              failedJobs:
                items:
                  type: string
                type: array
              jobStatusList:
                items:
                  properties:
                    endTimestamp:
                      format: date-time
                      type: string
                    name:
                      type: string
                    restartCount:
                      format: int32
                      type: integer
                    runningHistories:
                      items:
                        properties:
                          endTimestamp:
                            format: date-time
                            type: string
                          startTimestamp:
                            format: date-time
                            type: string
                          state:
                            type: string
                        type: object
                      type: array
                    startTimestamp:
                      format: date-time
                      type: string
                    state:
                      type: string
                  type: object
                type: array
              pendingJobs:
                items:
                  type: string
                type: array
              runningJobs:
                items:
                  type: string
                type: array
              state:
                properties:
                  phase:
                    type: string
                type: object
              terminatedJobs:
                items:
                  type: string
                type: array
              unKnowJobs:
                items:
                  type: string
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
