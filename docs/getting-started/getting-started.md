# Welcome to Volcano

Volcano is a batch system built on Kubernetes. It provides a suite of mechanisms currently missing from
Kubernetes that are commonly required by many classes of batch & elastic workload including:

1. machine learning/deep learning,
2. bioinformatics/genomics
3. other "big data" applications.

These types of applications typically run on generalized domain
frameworks like TensorFlow, Spark, Ray, PyTorch, MPI, etc, which <PERSON><PERSON> integrates with.

### Why Volcano?
- // TODO better to add separate md file & Link
- Learn about Volcano [here](https://github.com/volcano-sh/volcano/blob/master/README.md)

### First Steps
To get the most out of Volcano, start by reviewing a few introductory topics:
- [perepare-for-development](../development/prepare-for-development.md) - preoaration for development
- [Setup](../development/development.md) - Install Volcano
- [Contributing](https://github.com/volcano-sh/volcano/blob/master/contribute.md) - Contribute to Volcano
- [Troubleshooting](../troubleshooting/troubleshooting.md) - Troubleshoot commonly occurring issues. GitHub issues are [here](https://github.com/volcano-sh/volcano/issues)
