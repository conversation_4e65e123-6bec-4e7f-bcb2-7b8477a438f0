# Configuration for probot-stale - https://github.com/probot/stale

# Only issues or pull requests with all of these labels are check if stale. Defaults to `[]` (disabled)
onlyLabels: []

# Issues or Pull Requests with these labels will never be considered stale. Set to `[]` to disable.
# We want stale bot to notify us that something is stale so we can revisit it.
# If one issue is marked as 'reminder' by the reminder bot, we don't mark it as 'stale' again.
exemptLabels:
  # This label is hardcoded on remind bot (https://probot.github.io/apps/reminders/) and is used by remind bot when
  # issue is being reminded.
  - reminder

# Set to true to ignore issues in a project (defaults to false)
exemptProjects: false
# Set to true to ignore issues in a milestone (defaults to false)
exemptMilestones: false
# Set to true to ignore issues with an assignee (defaults to false)
exemptAssignees: false
# Label to use when marking as stale
staleLabel: lifecycle/stale

pull:
  daysUntilClose: 60
  daysUntilStale: 90
  markComment: >
    Hello 👋 Looks like there was no activity on this amazing PR for last 90 days.

    **Do you mind updating us on the status?** Is there anything we can help with? If you plan to still work on it, just comment on this PR or push a commit. Thanks! 🤗

    If there will be no activity for 60 days, this issue will be closed (we can always reopen a PR if you get back to this!).
  #unmarkComment: No need for unmark comment.
  closeComment: >
    Closing for now as there was no activity for last 60 days after marked as stale, let us know if you need this to be reopened! 🤗

issues:
  daysUntilClose: 60
  daysUntilStale: 90
  markComment: >
    Hello 👋 Looks like there was no activity on this issue for last 90 days.

    **Do you mind updating us on the status?** Is this still reproducible or needed? If yes, just comment on this PR or push a commit. Thanks! 🤗

    If there will be no activity for 60 days, this issue will be closed (we can always reopen an issue if we need!).
  #unmarkComment: No need for unmark comment.
  closeComment: >
    Closing for now as there was no activity for last 60 days after marked as stale, let us know if you need this to be reopened! 🤗

# Limit the number of actions per hour, from 1-30. Default is 30
limitPerRun: 30
