name: Code Verify

on:
  push:
    branches:
      - master
    tags:
  pull_request:

jobs:
  verify:
    runs-on: ubuntu-22.04
    name: Verify codes, generated files
    timeout-minutes: 40
    env:
      GOPATH: /home/<USER>/work/${{ github.repository }}
    steps:
      - name: Install Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          path: ./src/github.com/${{ github.repository }}

      - uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}

      - name: Run verify test
        run: |
          make lint
          make verify
          make TAG=v1.9.1 generate-yaml
          make verify-generated-yaml
          make unit-test
        working-directory: ./src/github.com/${{ github.repository }}
