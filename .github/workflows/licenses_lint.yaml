name: Licenses Lint

on:
  push:
    branches:
      - master
    tags:
  pull_request:

jobs:
  licenses-lint:
    name: Licenses Lint
    timeout-minutes: 40
    runs-on: ubuntu-22.04
    steps:
      - name: Install Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x
      - name: Checkout code
        uses: actions/checkout@v3
      - name: generate license mirror
        run: |
          make licenses-check
      - name: lint-licenses
        # if restricted > 0, CI will report an error.
        run: |
          make lint-licenses
