# Change Log

## [v0.4.2](https://github.com/volcano-sh/volcano/tree/v0.4) (2020-07-31)

- Fix queue capability validation failed when some running jobs finished or deleted ([#959](https://github.com/volcano-sh/volcano/pull/959), [@Thor-wl](https://github.com/Thor-wl))

## [v0.4.1](https://github.com/volcano-sh/volcano/tree/v0.4) (2020-07-14)

- UpdateJob when Job annotations or labels changed ([#919](https://github.com/volcano-sh/volcano/pull/919), [@Thor-wl](https://github.com/Thor-wl))
- Fix panic in controller ([#901](https://github.com/volcano-sh/volcano/pull/901), [@Thor-wl](https://github.com/Thor-wl))
- Support scale up and down ([#796](https://github.com/volcano-sh/volcano/pull/796), [@hzxuzhonghu](https://github.com/hzxuzhonghu))
- Fix crd to support job patch ops ([#794](https://github.com/volcano-sh/volcano/pull/794), [@hzxuzhonghu](https://github.com/hzxuzhonghu))

## [v0.4](https://github.com/volcano-sh/volcano/tree/v0.4) (2020-04-07)

- [#756](https://github.com/volcano-sh/volcano/pull/756) [controller] Set `BlockOwnerDeletion` to true when create PodGroup (@xiaogaozi)
- [#754](https://github.com/volcano-sh/volcano/pull/754) [controller] Set `Queue` field when pod has queue name annotation  (@xiaogaozi)
- [#746](https://github.com/volcano-sh/volcano/pull/746) Fix volcano job phase setting  (@hzxuzhonghu)
- [#745](https://github.com/volcano-sh/volcano/pull/745) Use go mod to manage dependencies  (@tizhou86)
- [#733](https://github.com/volcano-sh/volcano/pull/733) Added resources predicate in allocate action  (@k82cn)
- [#722](https://github.com/volcano-sh/volcano/pull/722) Added a flag `disable-network-policy` to disable Network Policy (@EDGsheryl)
- [#709](https://github.com/volcano-sh/volcano/pull/709) Use openssl to sign certificate instead of using k8s  (@hzxuzhonghu)
- [#702](https://github.com/volcano-sh/volcano/pull/702) Added env var for scheduler name  (@k82cn)
- [#693](https://github.com/volcano-sh/volcano/pull/693) Remove scheduling.v1alpha1 and scheduling.v1alpha2 API  (@thandayuthapani)
- [#681](https://github.com/volcano-sh/volcano/pull/681) Refactor events/action  (@k82cn)


## [v0.3](https://github.com/volcano-sh/volcano/tree/v0.3) (2020-01-20)

- [#670](https://github.com/volcano-sh/volcano/pull/670) Added Shareit as one of adopter (@k82cn)
- [#666](https://github.com/volcano-sh/volcano/pull/666) Add command vjobs, vqueues and unit tests. (@jiangkaihua)
- [#667](https://github.com/volcano-sh/volcano/pull/667) Revert "Gen install yaml without v1alpha queue and poggroup" (@hzxuzhonghu)
- [#664](https://github.com/volcano-sh/volcano/pull/664) Add capability in crd declarition (@hzxuzhonghu)
- [#663](https://github.com/volcano-sh/volcano/pull/663) add defaultQPS and defaultBurst for webhook-manager (@yuzhaojing)
- [#661](https://github.com/volcano-sh/volcano/pull/661) Change queue update in cli and e2e test to patch (@sivanzcw)
- [#660](https://github.com/volcano-sh/volcano/pull/660) Build new CLI by default. (@k82cn)
- [#656](https://github.com/volcano-sh/volcano/pull/656) Add vcommands: vcancel, vsuspend, vresume. (@jiangkaihua)
- [#658](https://github.com/volcano-sh/volcano/pull/658) Added scheduling v1beta1 API. (@k82cn)
- [#659](https://github.com/volcano-sh/volcano/pull/659) Add admission for queue (@sivanzcw)
- [#634](https://github.com/volcano-sh/volcano/pull/634) Do not create jobs until pg inqueue (@hzxuzhonghu)
- [#651](https://github.com/volcano-sh/volcano/pull/651) Reclaim Enhancement: Add Reclaimable parameter for queue (@sivanzcw)
- [#655](https://github.com/volcano-sh/volcano/pull/655) Auto generate code, change Copyright 2019 to Copyright 2020 (@sivanzcw)
- [#647](https://github.com/volcano-sh/volcano/pull/647) Considering best-effort pods when calculating ready task number (@sivanzcw)
- [#653](https://github.com/volcano-sh/volcano/pull/653) Gen install yaml without v1alpha queue and poggroup (@hzxuzhonghu)
- [#654](https://github.com/volcano-sh/volcano/pull/654) Remove pdb support (@hzxuzhonghu, @k82cn)
- [#652](https://github.com/volcano-sh/volcano/pull/652) Use relative path for doc. (@k82cn)
- [#633](https://github.com/volcano-sh/volcano/pull/633) fix the the Getting started link in contribute.md (@ruiyinchen)
- [#644](https://github.com/volcano-sh/volcano/pull/644) Refactor webhook org. (@k82cn)
- [#642](https://github.com/volcano-sh/volcano/pull/642) Added cherry_pick_pull.sh (@k82cn)
- [#643](https://github.com/volcano-sh/volcano/pull/643) Added Volcano Intro. (@k82cn)
- [#638](https://github.com/volcano-sh/volcano/pull/638) Remove duplicated check in jobEnqueueableFn of proportion (@zionwu)
- [#637](https://github.com/volcano-sh/volcano/pull/637) Update version to 0.3 (@k82cn)
- [#630](https://github.com/volcano-sh/volcano/pull/630) Added Roadmap (@k82cn)
- [#636](https://github.com/volcano-sh/volcano/pull/636) Modify error check of return error (@sivanzcw)
- [#631](https://github.com/volcano-sh/volcano/pull/631) Push job back to queue if task is assigned in reclaim action (@zionwu)
- [#632](https://github.com/volcano-sh/volcano/pull/632) remove redundant type conversion (@YesterdayxD)
- [#605](https://github.com/volcano-sh/volcano/pull/605) Rename binaries. (@k82cn)
- [#627](https://github.com/volcano-sh/volcano/pull/627) remove repeated code. (@YesterdayxD)
- [#626](https://github.com/volcano-sh/volcano/pull/626) Added Xiaohongshu as one of adopters (@k82cn)
- [#625](https://github.com/volcano-sh/volcano/pull/625) Update job_controller_util.go (@YesterdayxD)
- [#541](https://github.com/volcano-sh/volcano/pull/541) Pipeline task if task's request resource less than the releasing resource of node during performing allocate action (@sivanzcw)
- [#622](https://github.com/volcano-sh/volcano/pull/622) vcctl command line enhancement (@jiangkaihua)
- [#610](https://github.com/volcano-sh/volcano/pull/610) Added hosts into environment. (@k82cn)
- [#614](https://github.com/volcano-sh/volcano/pull/614) Update factory.go (@YesterdayxD)
- [#613](https://github.com/volcano-sh/volcano/pull/613) Added VC_TASK_INDEX and added env to initContainers. (@k82cn)
- [#609](https://github.com/volcano-sh/volcano/pull/609) Fixed build error of release-pkg. (@k82cn)
- [#608](https://github.com/volcano-sh/volcano/pull/608) Enahcement cli. (@k82cn)
- [#607](https://github.com/volcano-sh/volcano/pull/607) Fixed localup cluster script. (@k82cn)
- [#606](https://github.com/volcano-sh/volcano/pull/606) Update webhook path. (@k82cn)
- [#575](https://github.com/volcano-sh/volcano/pull/575) Admission Refactor. (@k82cn)
- [#603](https://github.com/volcano-sh/volcano/pull/603) change storage of ssh pem from configmap to secret for ssh plugin (@sivanzcw)
- [#601](https://github.com/volcano-sh/volcano/pull/601) Added localup script. (@k82cn)
- [#600](https://github.com/volcano-sh/volcano/pull/600) Remove kar, kube-batch. (@k82cn)
- [#599](https://github.com/volcano-sh/volcano/pull/599) Change lessequal function in Reclaimable function (@sivanzcw)
- [#597](https://github.com/volcano-sh/volcano/pull/597) when delete pod, a new shadowgroup will be created (@invalid-email-address)
- [#570](https://github.com/volcano-sh/volcano/pull/570) added priority based preemption to priority plugin (@mateuszlitwin)
- [#588](https://github.com/volcano-sh/volcano/pull/588) Cleanup e2e framework to speed up e2e (@hzxuzhonghu)
- [#591](https://github.com/volcano-sh/volcano/pull/591) disp job in default queue (@jiangkaihua)
- [#590](https://github.com/volcano-sh/volcano/pull/590) Support queue action by vcctl (@sivanzcw)
- [#589](https://github.com/volcano-sh/volcano/pull/589) Upgrade helm to v3.0.1 (@hzxuzhonghu)
- [#592](https://github.com/volcano-sh/volcano/pull/592) Add Vivo as adopter (@k82cn)
- [#587](https://github.com/volcano-sh/volcano/pull/587) Add arguments for action (@sivanzcw)
- [#585](https://github.com/volcano-sh/volcano/pull/585) use future idle resources when checking if task can fit node (@mateuszlitwin)
- [#512](https://github.com/volcano-sh/volcano/pull/512) Add queue controller about state (@sivanzcw)
- [#586](https://github.com/volcano-sh/volcano/pull/586) dep ensure (@sivanzcw)
- [#584](https://github.com/volcano-sh/volcano/pull/584) change node not found errors (@invalid-email-address)
- [#581](https://github.com/volcano-sh/volcano/pull/581) Change Statement unevict method to call UpdateTask (@yodarshafrir1)
- [#578](https://github.com/volcano-sh/volcano/pull/578) Add explicit info about what todo to update generated yaml (@hzxuzhonghu)
- [#577](https://github.com/volcano-sh/volcano/pull/577) Enable CI verify (@hzxuzhonghu)
- [#576](https://github.com/volcano-sh/volcano/pull/576) Enable networkpolicy create/get permission (@hzxuzhonghu)
- [#572](https://github.com/volcano-sh/volcano/pull/572) fix validate victims check for preempt action (@zionwu)
- [#567](https://github.com/volcano-sh/volcano/pull/567) Update admission to use pflag. (@k82cn)
- [#564](https://github.com/volcano-sh/volcano/pull/564) Fixed build error. (@k82cn)
- [#566](https://github.com/volcano-sh/volcano/pull/566) Fix wrong condition for reclaim action (@zionwu)
- [#563](https://github.com/volcano-sh/volcano/pull/563) Update to klog. (@k82cn)
- [#542](https://github.com/volcano-sh/volcano/pull/542) modify the 'vcctl job run' function (@jiangkaihua)
- [#552](https://github.com/volcano-sh/volcano/pull/552) Support networkpolicy (@hzxuzhonghu)
- [#560](https://github.com/volcano-sh/volcano/pull/560) Move myself to controller owner (@hzxuzhonghu)
- [#547](https://github.com/volcano-sh/volcano/pull/547) Modify comments on OnPodCreate function of svc plugin (@sivanzcw)
- [#544](https://github.com/volcano-sh/volcano/pull/544) Simplify job pvc create process (@hzxuzhonghu)
- [#515](https://github.com/volcano-sh/volcano/pull/515) ssh plugin support specifying private/public keys path (@hzxuzhonghu)
- [#537](https://github.com/volcano-sh/volcano/pull/537) Add queueAction queueEvent queueRequest type (@sivanzcw)
- [#536](https://github.com/volcano-sh/volcano/pull/536) Add QTT as adopter (@k82cn)
- [#535](https://github.com/volcano-sh/volcano/pull/535) Add the --publish-not-ready-addresses param for the svc plugin (@zrss)
- [#527](https://github.com/volcano-sh/volcano/pull/527) Add svc hosts volumeMount for InitContainers (@zrss)
- [#525](https://github.com/volcano-sh/volcano/pull/525) Fixed import order. (@k82cn)
- [#523](https://github.com/volcano-sh/volcano/pull/523) pdb bug 修复 (@chenshaojin)
- [#520](https://github.com/volcano-sh/volcano/pull/520) Modify scheduling events for pod and podgroup (@sivanzcw)
- [#517](https://github.com/volcano-sh/volcano/pull/517) Add filter function for command watching of job controller (@sivanzcw)
- [#518](https://github.com/volcano-sh/volcano/pull/518) Added Gitter (@k82cn)
- [#507](https://github.com/volcano-sh/volcano/pull/507) fix filter NotReady node (@wangyuqing4)
- [#513](https://github.com/volcano-sh/volcano/pull/513) fix podgroup phase (@wangyuqing4)
- [#511](https://github.com/volcano-sh/volcano/pull/511) Umbrealla cleanups (@wangyuqing4)
- [#510](https://github.com/volcano-sh/volcano/pull/510) Rename imported package alias (@hzxuzhonghu)
- [#508](https://github.com/volcano-sh/volcano/pull/508) Add state parameter to queueSpec and queueStatus for queue (@sivanzcw)
- [#501](https://github.com/volcano-sh/volcano/pull/501) Add queue state management design proposal (@sivanzcw)
- [#506](https://github.com/volcano-sh/volcano/pull/506) Add events for pod with pipelined state (@sivanzcw)
- [#504](https://github.com/volcano-sh/volcano/pull/504) Dynamic loading comfigmap about action and plugins of scheduler, move loadSchedulerConf processing from run to runOnce (@sivanzcw)
- [#502](https://github.com/volcano-sh/volcano/pull/502) Fix deprecated dind in favor of kind in develop doc (@akillcool)
- [#499](https://github.com/volcano-sh/volcano/pull/499) correct podgroup creating bug for single pod without ownerreference (@sivanzcw)
- [#498](https://github.com/volcano-sh/volcano/pull/498) refresh volumes logic (@lminzhw, @dingtsh1)
- [#500](https://github.com/volcano-sh/volcano/pull/500) Request to be a reviewer (@yuanchen8911)
- [#497](https://github.com/volcano-sh/volcano/pull/497) add priorityClassName to podgroup during creating of podgroup from pod (@sivanzcw)
- [#491](https://github.com/volcano-sh/volcano/pull/491) Control the number of feasible nodes to find and score in scheduling (@yuanchen8911)
- [#494](https://github.com/volcano-sh/volcano/pull/494) format function name (@hzxuzhonghu)
- [#493](https://github.com/volcano-sh/volcano/pull/493) Added execution flow img. (@k82cn)
- [#490](https://github.com/volcano-sh/volcano/pull/490) Updated version to 0.2 (@k82cn)
- [#489](https://github.com/volcano-sh/volcano/pull/489) Added svg logo. (@k82cn)
- [#488](https://github.com/volcano-sh/volcano/pull/488) Admission: Fall back to v1alpha1 podgroup when v1alpha2 doesnot exist (@hzxuzhonghu)
- [#486](https://github.com/volcano-sh/volcano/pull/486) fix vvctl e2e (@hzxuzhonghu)
- [#485](https://github.com/volcano-sh/volcano/pull/485) Support KUBECON env. (@k82cn)
- [#478](https://github.com/volcano-sh/volcano/pull/478) check  while ~/.kube/config is missing (@Rui-Tang)
- [#484](https://github.com/volcano-sh/volcano/pull/484) fix Resource Less/LessEqual (@wangyuqing4)
- [#482](https://github.com/volcano-sh/volcano/pull/482) fix proportion OverusedFn (@wangyuqing4)
- [#473](https://github.com/volcano-sh/volcano/pull/473) Comment out job volumes. (@k82cn)
- [#471](https://github.com/volcano-sh/volcano/pull/471) Rename file name of volcano intro in HC. (@k82cn)
- [#468](https://github.com/volcano-sh/volcano/pull/468) Added talks & integration into readme. (@k82cn)
- [#460](https://github.com/volcano-sh/volcano/pull/460) modify the return value of 'vcctl' (@jiangkaihua)
- [#463](https://github.com/volcano-sh/volcano/pull/463) Add HC demo. (@k82cn)
- [#451](https://github.com/volcano-sh/volcano/pull/451) Add Huawei-Cloud and GrandOmics (@k82cn)
- [#445](https://github.com/volcano-sh/volcano/pull/445) Queue refactor. (@k82cn)
- [#446](https://github.com/volcano-sh/volcano/pull/446) Register healthz interface for controller and scheduler (@sivanzcw)
- [#444](https://github.com/volcano-sh/volcano/pull/444) bump golang version to 1.13.x and kind to v0.5.0 in ci (@hzxuzhonghu)
- [#443](https://github.com/volcano-sh/volcano/pull/443) Add Baidu as adopter of Volcano. (@tizhou86)
- [#440](https://github.com/volcano-sh/volcano/pull/440) simplify README (@k82cn)
- [#442](https://github.com/volcano-sh/volcano/pull/442) Added adopters of Volcano. (@k82cn)
- [#439](https://github.com/volcano-sh/volcano/pull/439) set the json name of exitCode (@davidstack)
- [#437](https://github.com/volcano-sh/volcano/pull/437) Change image repository of mxnet demo from private to volcanosh (@sivanzcw)
- [#412](https://github.com/volcano-sh/volcano/pull/412) Add maxRetry in job controller to prevent endless loop (@hzxuzhonghu)
- [#411](https://github.com/volcano-sh/volcano/pull/411) Skip verify volcano job container's Privileged mode (@hzxuzhonghu)
- [#433](https://github.com/volcano-sh/volcano/pull/433) Fix CRD Definition (@hzxuzhonghu)
- [#434](https://github.com/volcano-sh/volcano/pull/434) Add demo about Click-Through-Rate distributed training with PaddlePad… (@sivanzcw)
- [#431](https://github.com/volcano-sh/volcano/pull/431) Moved KubeCon 2019 China demo to example. (@k82cn)

## [v0.2](https://github.com/volcano-sh/volcano/tree/v0.2) (2019-09-03)

- [#429](https://github.com/volcano-sh/volcano/pull/429) Remove namespace for ClusterRole&ClusterRoleBinding (@fisherxu)
- [#358](https://github.com/volcano-sh/volcano/pull/358) support fair share (@lminzhw)
- [#422](https://github.com/volcano-sh/volcano/pull/422) Updated volcano logo (@k82cn)
- [#421](https://github.com/volcano-sh/volcano/pull/421) remove inqueue job phase (@wangyuqing4)
- [#424](https://github.com/volcano-sh/volcano/pull/424) Improve resources convert between v1alpha1 and v1alpha2 for scheduling group (@hzxuzhonghu)
- [#381](https://github.com/volcano-sh/volcano/pull/381) E2E for TensorFlow Integration (@thandayuthapani)
- [#420](https://github.com/volcano-sh/volcano/pull/420) Fixed typo in error message. (@k82cn)
- [#396](https://github.com/volcano-sh/volcano/pull/396) [cleanup] Only generate required scheduler configmap (@TommyLike)
- [#419](https://github.com/volcano-sh/volcano/pull/419) Added enqueue action. (@k82cn)
- [#418](https://github.com/volcano-sh/volcano/pull/418) Added "enqueue" to the actions in defaultSchedulerConf to fix a bug (@yuanchen8911)
- [#415](https://github.com/volcano-sh/volcano/pull/415) Bump k8s dependencies version (@TommyLike)
- [#414](https://github.com/volcano-sh/volcano/pull/414) Revert code coverage temporarily (@hzxuzhonghu)
- [#370](https://github.com/volcano-sh/volcano/pull/370) add admitPod (@wangyuqing4)
- [#408](https://github.com/volcano-sh/volcano/pull/408) fix: Add comments about the defaulting of queue (@gaocegege)
- [#406](https://github.com/volcano-sh/volcano/pull/406) remove shadowPodgroup in scheduler (@wangyuqing4)
- [#405](https://github.com/volcano-sh/volcano/pull/405) clean some redundant  job/pg/gc controllers code (@wangyuqing4)
- [#385](https://github.com/volcano-sh/volcano/pull/385) Fix error resync logic (@hzxuzhonghu)
- [#401](https://github.com/volcano-sh/volcano/pull/401) add podgroup controller (@wangyuqing4)
- [#331](https://github.com/volcano-sh/volcano/pull/331) E2E test scenarios for task policy (@Rajadeepan)
- [#399](https://github.com/volcano-sh/volcano/pull/399) Add `Inqueue` status (@hzxuzhonghu)
- [#398](https://github.com/volcano-sh/volcano/pull/398) Make use of test.Run (@TommyLike)
- [#395](https://github.com/volcano-sh/volcano/pull/395) Add E2E for check functionality of all Plugins (@thandayuthapani)
- [#391](https://github.com/volcano-sh/volcano/pull/391) Fix race condition issue (@TommyLike)
- [#380](https://github.com/volcano-sh/volcano/pull/380) support binpack policy (@lminzhw)
- [#393](https://github.com/volcano-sh/volcano/pull/393) Add E2E to verify Event Generation for Volcano Job (@thandayuthapani)
- [#392](https://github.com/volcano-sh/volcano/pull/392) Add Best practises badge (@asifdxtreme)
- [#364](https://github.com/volcano-sh/volcano/pull/364) migrate queue/podgroup to v1alpha2 (@hzxuzhonghu)
- [#383](https://github.com/volcano-sh/volcano/pull/383) E2E Task Priority (@Rajadeepan)
- [#379](https://github.com/volcano-sh/volcano/pull/379) added e2e test case with exist pvc in job (@SrinivasChilveri)
- [#382](https://github.com/volcano-sh/volcano/pull/382) E2E admission (@Rajadeepan)
- [#387](https://github.com/volcano-sh/volcano/pull/387) Add all valid policy actions (@hzxuzhonghu)
- [#373](https://github.com/volcano-sh/volcano/pull/373) Add Verify Job to check consistency between generate yaml and the yam… (@asifdxtreme)
- [#352](https://github.com/volcano-sh/volcano/pull/352) Add development guide (@SrinivasChilveri, @hzxuzhonghu)
- [#375](https://github.com/volcano-sh/volcano/pull/375) E2E additional scenario (@Rajadeepan)
- [#374](https://github.com/volcano-sh/volcano/pull/374) Add E2E for scheduling v1.Job using volcano scheduler (@thandayuthapani)
- [#372](https://github.com/volcano-sh/volcano/pull/372) Publish Docker Image in Daily Build (@asifdxtreme)
- [#363](https://github.com/volcano-sh/volcano/pull/363) E2E job life cycle (@Rajadeepan)
- [#366](https://github.com/volcano-sh/volcano/pull/366) E2E for Predicates (@thandayuthapani)
- [#362](https://github.com/volcano-sh/volcano/pull/362) Some cleanups after kube-batch intree (@hzxuzhonghu)
- [#356](https://github.com/volcano-sh/volcano/pull/356) Read me slack links were not working so changed the same (@SrinivasChilveri)
- [#345](https://github.com/volcano-sh/volcano/pull/345) Add one click install script (@hzxuzhonghu)
- [#351](https://github.com/volcano-sh/volcano/pull/351) Added fossa check. (@k82cn)
- [#350](https://github.com/volcano-sh/volcano/pull/350) Add design (@k82cn)
- [#349](https://github.com/volcano-sh/volcano/pull/349) Added Volcano introduction. (@k82cn)
- [#346](https://github.com/volcano-sh/volcano/pull/346) Update readme document (@TommyLike)
- [#344](https://github.com/volcano-sh/volcano/pull/344) Support release volcano in Travis (@TommyLike)
- [#343](https://github.com/volcano-sh/volcano/pull/343) E2E for vcctl help option (@thandayuthapani)
- [#342](https://github.com/volcano-sh/volcano/pull/342) add slack invitation to project readme (@kevin-wangzefeng)
- [#332](https://github.com/volcano-sh/volcano/pull/332) Register webhook in codes (@TommyLike)
- [#314](https://github.com/volcano-sh/volcano/pull/314) Support v1alpha2 version of PodGroup & Queue (@thandayuthapani)
- [#341](https://github.com/volcano-sh/volcano/pull/341) Fix default queue create issue (@TommyLike)
- [#340](https://github.com/volcano-sh/volcano/pull/340) Change vkctl to vcctl (@thandayuthapani)
- [#330](https://github.com/volcano-sh/volcano/pull/330) omit pods not controlled by volcano (@hzxuzhonghu)
- [#337](https://github.com/volcano-sh/volcano/pull/337) fix ExitCode panic (@wangyuqing4)
- [#336](https://github.com/volcano-sh/volcano/pull/336) vkctl job list --all-namespaces show ns (@wangyuqing4)
- [#334](https://github.com/volcano-sh/volcano/pull/334) Adding helm chart templates back (@TommyLike)
- [#190](https://github.com/volcano-sh/volcano/pull/190) sync job works concurrently (@SrinivasChilveri)
- [#315](https://github.com/volcano-sh/volcano/pull/315) Adding e2e test case for multiple events in lifecycle policy (@Rajadeepan)
- [#321](https://github.com/volcano-sh/volcano/pull/321) remove hardcoded plugin name (@hzxuzhonghu)
- [#326](https://github.com/volcano-sh/volcano/pull/326) Fix issue in makefile (@SrinivasChilveri)
- [#313](https://github.com/volcano-sh/volcano/pull/313) Update kube-batch to scheduler (@asifdxtreme)
- [#322](https://github.com/volcano-sh/volcano/pull/322) change SchedulerName in cli from kube-batch to volcano (@wangyuqing4)
- [#320](https://github.com/volcano-sh/volcano/pull/320) set drf dominant resource name (@hzxuzhonghu)
- [#319](https://github.com/volcano-sh/volcano/pull/319) Fix vk & vc issue (@TommyLike)
- [#316](https://github.com/volcano-sh/volcano/pull/316) Removing logs which does not belong to volcano job (@Rajadeepan)
- [#312](https://github.com/volcano-sh/volcano/pull/312) Update vk- to vc- (@asifdxtreme)
- [#285](https://github.com/volcano-sh/volcano/pull/285) Support multiple events in the lifecycle policy (@Rajadeepan)
- [#309](https://github.com/volcano-sh/volcano/pull/309) calc PGMinResources for Resources.Limits (@wangyuqing4)
- [#295](https://github.com/volcano-sh/volcano/pull/295) Updating submodule before testing (@TommyLike)
- [#307](https://github.com/volcano-sh/volcano/pull/307) Some yaml fix and cleanups after kube-batch in tree (@hzxuzhonghu)
- [#306](https://github.com/volcano-sh/volcano/pull/306) Fix the scheduler panic whenever the GPU is lost on node (@william-wang)
- [#303](https://github.com/volcano-sh/volcano/pull/303) dep ensure (@hzxuzhonghu, @k82cn)
- [#302](https://github.com/volcano-sh/volcano/pull/302) move hzxuzhonghu to scheduler owners (@hzxuzhonghu)
- [#301](https://github.com/volcano-sh/volcano/pull/301) Add tiller log when process failed (@TommyLike)
- [#297](https://github.com/volcano-sh/volcano/pull/297) Update README.md (@k82cn)
- [#296](https://github.com/volcano-sh/volcano/pull/296) Add kube-batch notes. (@k82cn)
- [#288](https://github.com/volcano-sh/volcano/pull/288) Scheduler in tree (@kevin-wangzefeng, @k82cn, @ConnorDoyle, @jinzhejz, @asifdxtreme, @mbssaiakhil, @hanghliu, @swiftdiaries, @spiffxp, @gaocegege, @mitake, @kkaneda, @raoofm, @yanniszark, @SpandanKumarSahu, @rc-zhang, @sedflix, @dmatch01, @scostache, @etiennecoutaud, @wackxu, @suleisl2000, @ChanYiLin, @tizhou86, @denkensk, @Atul9, @chenyangxueHDU, @jeefy, @animeshsingh, @adam-marek, @mooncak, @xichengliudui, @goodluckbot, @rexxar-liang, @jiaxuanzhou, @SataQiu, @Jeffwan, @Zyqsempai, @mateusz-ciesielski, @thandayuthapani, @hex108, @DeliangFan, @TommyLike, @zionwu, @lmzqwer2, @yylin1, @hzxuzhonghu, @wangyuqing4, @jimbobby5, @dingtsh1, @nikita15p, @lminzhw, @sivanzcw, @william-wang)
- [#292](https://github.com/volcano-sh/volcano/pull/292) Update readme document (@TommyLike)
- [#291](https://github.com/volcano-sh/volcano/pull/291) migrate image to volcanosh (@hzxuzhonghu)
- [#289](https://github.com/volcano-sh/volcano/pull/289) count unknown pod as [Unknown] in job status (@lminzhw)
- [#278](https://github.com/volcano-sh/volcano/pull/278) Propose TommyLike as reviewers and approvers of controller packages (@TommyLike)
- [#287](https://github.com/volcano-sh/volcano/pull/287) add scripts for syncing scheduler code into tree (@kevin-wangzefeng)
- [#281](https://github.com/volcano-sh/volcano/pull/281) Inherit kube-batch owners (@k82cn)
- [#284](https://github.com/volcano-sh/volcano/pull/284) Create base image for admission service (@TommyLike)
- [#253](https://github.com/volcano-sh/volcano/pull/253) add more vkctl command and fix bugs (@lminzhw, @wangyuqing4)
- [#283](https://github.com/volcano-sh/volcano/pull/283) Fix update issue (@TommyLike)
- [#254](https://github.com/volcano-sh/volcano/pull/254) fix some bug about event and workqueue (@lminzhw, @wangyuqing4)
- [#277](https://github.com/volcano-sh/volcano/pull/277) Fix log issue when failed to update job (@TommyLike)
- [#276](https://github.com/volcano-sh/volcano/pull/276) Lowercase first letter in json response (@TommyLike)
- [#279](https://github.com/volcano-sh/volcano/pull/279) Try kind 0.4.0 for travis (@TommyLike)
- [#273](https://github.com/volcano-sh/volcano/pull/273) Additional UT in cli, queue,jo info pkg (@Rajadeepan)
- [#271](https://github.com/volcano-sh/volcano/pull/271) UT Cases for Job Package (@thandayuthapani)
- [#269](https://github.com/volcano-sh/volcano/pull/269) Bump Volcano-sh/Scheduler (@asifdxtreme)
- [#270](https://github.com/volcano-sh/volcano/pull/270) Enable coverage stats for UT (@thandayuthapani)
- [#265](https://github.com/volcano-sh/volcano/pull/265) Update .travis.yml (@k82cn)
- [#258](https://github.com/volcano-sh/volcano/pull/258) Adding UT for Queue pkg under cli (@shivramsrivastava)
- [#263](https://github.com/volcano-sh/volcano/pull/263) Add talks section to Readme (@asifdxtreme)
- [#255](https://github.com/volcano-sh/volcano/pull/255) init job status to Pending (@lminzhw, @wangyuqing4)
- [#262](https://github.com/volcano-sh/volcano/pull/262) fix some words in README doc (@soolaugust)
- [#252](https://github.com/volcano-sh/volcano/pull/252) Improving the code coverage for admission controller pkg (@shivramsrivastava)
- [#259](https://github.com/volcano-sh/volcano/pull/259) Kubecon 2019 china demo (@k82cn)
- [#257](https://github.com/volcano-sh/volcano/pull/257) Adding UT for cli job package (@Rajadeepan)
- [#256](https://github.com/volcano-sh/volcano/pull/256) support err task resync (@wangyuqing4)
- [#234](https://github.com/volcano-sh/volcano/pull/234) Lint fix for admission and e2e package except dot import lint failures (@nikita15p)
- [#247](https://github.com/volcano-sh/volcano/pull/247) UT cases for garbagecollector package (@thandayuthapani)
- [#251](https://github.com/volcano-sh/volcano/pull/251) Adding UT test cases to apis package (@Rajadeepan)
- [#250](https://github.com/volcano-sh/volcano/pull/250) UT cases for pkg/controllers/cache package (@thandayuthapani)
- [#249](https://github.com/volcano-sh/volcano/pull/249) Adding UT test cases to queue controller (@Rajadeepan)
- [#248](https://github.com/volcano-sh/volcano/pull/248) Lint fix for apis, cache, job and state package of controller (@nikita15p)
- [#238](https://github.com/volcano-sh/volcano/pull/238) Adding Events for Action (@Rajadeepan)
- [#245](https://github.com/volcano-sh/volcano/pull/245) Add UT cases for pkg/controllers/job/state package (@thandayuthapani)
- [#235](https://github.com/volcano-sh/volcano/pull/235) UT cases for Job Package - job_controller_actions.go (@thandayuthapani)
- [#243](https://github.com/volcano-sh/volcano/pull/243) Keep none preemptable job running (@TommyLike)
- [#236](https://github.com/volcano-sh/volcano/pull/236) Update README (@thandayuthapani)
- [#239](https://github.com/volcano-sh/volcano/pull/239) Cleanup codes and files (@TommyLike)
- [#240](https://github.com/volcano-sh/volcano/pull/240) Use submodule to integrate helm chart repo (@TommyLike)
- [#225](https://github.com/volcano-sh/volcano/pull/225) Lint Fixes (@SrinivasChilveri)
- [#230](https://github.com/volcano-sh/volcano/pull/230) Added/Updated license for copied scripts (@Rajadeepan)
- [#231](https://github.com/volcano-sh/volcano/pull/231) Import GCP when initializing client (@TommyLike)
- [#227](https://github.com/volcano-sh/volcano/pull/227) Fix some typos in documents (@fyuan1316)
- [#220](https://github.com/volcano-sh/volcano/pull/220) Bump Scheduler (@asifdxtreme)
- [#226](https://github.com/volcano-sh/volcano/pull/226) Fix E2E because of Event Change (@thandayuthapani)
- [#223](https://github.com/volcano-sh/volcano/pull/223) Fixing lint error in cmd package (@Rajadeepan)
- [#224](https://github.com/volcano-sh/volcano/pull/224) Added imagePullSecrete for scheduler in helm chart (@SrinivasChilveri)
- [#215](https://github.com/volcano-sh/volcano/pull/215) updated readme (@SrinivasChilveri)
- [#193](https://github.com/volcano-sh/volcano/pull/193) doc for drf fair share (@lminzhw)
- [#213](https://github.com/volcano-sh/volcano/pull/213) Added UT cases for Job Package -  job_controller_plugins (@thandayuthapani)
- [#212](https://github.com/volcano-sh/volcano/pull/212) Bump volcano-sh/kube-batch to volcano-sh/scheduler (@thandayuthapani)
- [#211](https://github.com/volcano-sh/volcano/pull/211) Fix job controller panic (@k82cn)
- [#210](https://github.com/volcano-sh/volcano/pull/210) Added svc plugin name in ControlledResources of job status (@SrinivasChilveri)
- [#209](https://github.com/volcano-sh/volcano/pull/209) Added UT for options in controller (@SrinivasChilveri)
- [#205](https://github.com/volcano-sh/volcano/pull/205) Check Queue exist in admission controller (@thandayuthapani)
- [#206](https://github.com/volcano-sh/volcano/pull/206) Update README.md (@k82cn)
- [#207](https://github.com/volcano-sh/volcano/pull/207) Remove reclaim and preempt by default (@thandayuthapani)
- [#201](https://github.com/volcano-sh/volcano/pull/201) UT cases for files in Job Controller pkg (@thandayuthapani)
- [#204](https://github.com/volcano-sh/volcano/pull/204) allowing the controller to update the configmaps (@SrinivasChilveri)
- [#191](https://github.com/volcano-sh/volcano/pull/191) Use Job instead of helm plugin to generate admission secret (@TommyLike)
- [#189](https://github.com/volcano-sh/volcano/pull/189) Remove unnecessary folder (@TommyLike)
- [#187](https://github.com/volcano-sh/volcano/pull/187) move scheduler name out of common args (@lminzhw)
- [#188](https://github.com/volcano-sh/volcano/pull/188) support vkctl job view (@lminzhw)
- [#186](https://github.com/volcano-sh/volcano/pull/186) Optimizing E2E test cases (@SrinivasChilveri)
- [#181](https://github.com/volcano-sh/volcano/pull/181) support vkctl job run --limits (@lminzhw)
- [#183](https://github.com/volcano-sh/volcano/pull/183) Removed repeated validation and  unused code (@SrinivasChilveri)
- [#180](https://github.com/volcano-sh/volcano/pull/180) Simple Issue Fixes in vkctl (@SrinivasChilveri)
- [#177](https://github.com/volcano-sh/volcano/pull/177) vkctl delete feature (@SrinivasChilveri)
- [#178](https://github.com/volcano-sh/volcano/pull/178) Added queue get command & changed list command to print status (@SrinivasChilveri)
- [#176](https://github.com/volcano-sh/volcano/pull/176) retain pod with special phase (@lminzhw)
- [#174](https://github.com/volcano-sh/volcano/pull/174) Support job level priorityClassName (@TommyLike)
- [#171](https://github.com/volcano-sh/volcano/pull/171) moved some of admission e2e test cases as ut test cases (@SrinivasChilveri)
- [#162](https://github.com/volcano-sh/volcano/pull/162) Fix calculation issue for extension resource in volcano. (@TommyLike)
- [#167](https://github.com/volcano-sh/volcano/pull/167) Added ttl validation in admission controller (@SrinivasChilveri)
- [#149](https://github.com/volcano-sh/volcano/pull/149) Cleanup Job after a configured ttl (@hzxuzhonghu)
- [#161](https://github.com/volcano-sh/volcano/pull/161) Rename scheduler to kube-batch (@asifdxtreme)
- [#157](https://github.com/volcano-sh/volcano/pull/157) Add Changelog for v0.1 Release (@asifdxtreme)
- [#158](https://github.com/volcano-sh/volcano/pull/158) Add JobType in CLI (@thandayuthapani)

## [v0.1](https://github.com/volcano-sh/volcano/tree/v0.1) (2019-05-11)

**Closed issues:**

- Propose moving volume claim validation&generate logic into admission hook [\#139](https://github.com/volcano-sh/volcano/issues/139)
- Sync codes from CodeClub [\#112](https://github.com/volcano-sh/volcano/issues/112)
- Sync kubemark patches into volcano [\#109](https://github.com/volcano-sh/volcano/issues/109)
- Makefile cleanup [\#105](https://github.com/volcano-sh/volcano/issues/105)
- Make OWNERS file for Volcano [\#102](https://github.com/volcano-sh/volcano/issues/102)
- Makefile cleanup [\#69](https://github.com/volcano-sh/volcano/issues/69)
- How do we support Job.Spec update [\#68](https://github.com/volcano-sh/volcano/issues/68)
- make mutating and validating admission controllers consistent [\#63](https://github.com/volcano-sh/volcano/issues/63)
- Labels key can not contain '/' [\#62](https://github.com/volcano-sh/volcano/issues/62)
- Job deletion handler should consider `DeletedFinalStateUnknown` [\#58](https://github.com/volcano-sh/volcano/issues/58)
- Unable get csr when building test cluster [\#54](https://github.com/volcano-sh/volcano/issues/54)
- Reclaim CI failed [\#49](https://github.com/volcano-sh/volcano/issues/49)
- Add example on MPI Job [\#42](https://github.com/volcano-sh/volcano/issues/42)
- Deleting helm chart exits with error [\#35](https://github.com/volcano-sh/volcano/issues/35)
- Update Imports [\#30](https://github.com/volcano-sh/volcano/issues/30)
- CI Build Failure [\#27](https://github.com/volcano-sh/volcano/issues/27)
- Queue controller and related cli [\#16](https://github.com/volcano-sh/volcano/issues/16)
- Update tensorflow example [\#146](https://github.com/volcano-sh/volcano/issues/146)
- Move vkctrl queue to Volcano from kube-batch [\#133](https://github.com/volcano-sh/volcano/issues/133)
- Cherry pick .spec.Capability of Queue [\#131](https://github.com/volcano-sh/volcano/issues/131)
- Sync combine input & output volumes into array logic. [\#122](https://github.com/volcano-sh/volcano/issues/122)
- Fix state machine issue [\#121](https://github.com/volcano-sh/volcano/issues/121)
- 11 tests are failed in CI [\#120](https://github.com/volcano-sh/volcano/issues/120)
- Set default value of PodGroup in admission controller [\#118](https://github.com/volcano-sh/volcano/issues/118)
- Update kube-batch dependency to master [\#114](https://github.com/volcano-sh/volcano/issues/114)
- Support printing version for binaries [\#77](https://github.com/volcano-sh/volcano/issues/77)
- The docker image name should align with binaries' [\#72](https://github.com/volcano-sh/volcano/issues/72)
- Update mpi example to use hostfile [\#71](https://github.com/volcano-sh/volcano/issues/71)
- Add error handling for exit code [\#55](https://github.com/volcano-sh/volcano/issues/55)
- Move job/cache job/apis to upper dir [\#43](https://github.com/volcano-sh/volcano/issues/43)
- Support Task/Job retry [\#40](https://github.com/volcano-sh/volcano/issues/40)
- Fix Failing testcases [\#32](https://github.com/volcano-sh/volcano/issues/32)
- Add e2e test for admission service [\#31](https://github.com/volcano-sh/volcano/issues/31)
- Support TaskSpec level error handling [\#26](https://github.com/volcano-sh/volcano/issues/26)
- Support Job plugins [\#14](https://github.com/volcano-sh/volcano/issues/14)
- Delay pod creation [\#12](https://github.com/volcano-sh/volcano/issues/12)
- Setup travis as CI env [\#7](https://github.com/volcano-sh/volcano/issues/7)

**Merged pull requests:**

- admission to get tls certificate from kubeconfig, if tls config not defined in command line [\#152](https://github.com/volcano-sh/volcano/pull/152) ([sivanzcw](https://github.com/sivanzcw))
- Update tf related resource files [\#150](https://github.com/volcano-sh/volcano/pull/150) ([TommyLike](https://github.com/TommyLike))
- Print detail message when wait timeout in tests [\#148](https://github.com/volcano-sh/volcano/pull/148) ([TommyLike](https://github.com/TommyLike))
- Return immediately when failed to generate admission secret [\#145](https://github.com/volcano-sh/volcano/pull/145) ([TommyLike](https://github.com/TommyLike))
- version related changes [\#144](https://github.com/volcano-sh/volcano/pull/144) ([SrinivasChilveri](https://github.com/SrinivasChilveri))
- Update volcano crds & sample files [\#143](https://github.com/volcano-sh/volcano/pull/143) ([TommyLike](https://github.com/TommyLike))
- Fix combine volume feature issues&Adding testcase [\#140](https://github.com/volcano-sh/volcano/pull/140) ([TommyLike](https://github.com/TommyLike))
- Bump Kube-batch version [\#138](https://github.com/volcano-sh/volcano/pull/138) ([asifdxtreme](https://github.com/asifdxtreme))
- Moving vkctrl queue to Volcano from kube-batch [\#136](https://github.com/volcano-sh/volcano/pull/136) ([Rajadeepan](https://github.com/Rajadeepan))
- Fix log tailed issue [\#132](https://github.com/volcano-sh/volcano/pull/132) ([TommyLike](https://github.com/TommyLike))
- remove enableNamespaceAsQueue in e2e [\#130](https://github.com/volcano-sh/volcano/pull/130) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Add queue controller [\#128](https://github.com/volcano-sh/volcano/pull/128) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Add default queue in admission hook [\#127](https://github.com/volcano-sh/volcano/pull/127) ([TommyLike](https://github.com/TommyLike))
- \[Issue \#121\]fix state convert [\#126](https://github.com/volcano-sh/volcano/pull/126) ([wangyuqing4](https://github.com/wangyuqing4))
- Combine input & output volumes [\#124](https://github.com/volcano-sh/volcano/pull/124) ([TommyLike](https://github.com/TommyLike))
- Move mxnet examples into integration folder [\#123](https://github.com/volcano-sh/volcano/pull/123) ([TommyLike](https://github.com/TommyLike))
- pkg improvement. [\#119](https://github.com/volcano-sh/volcano/pull/119) ([k82cn](https://github.com/k82cn))
- Updated kube-batch to vk-kube-batch. [\#115](https://github.com/volcano-sh/volcano/pull/115) ([k82cn](https://github.com/k82cn))
- Update kube batch source [\#111](https://github.com/volcano-sh/volcano/pull/111) ([TommyLike](https://github.com/TommyLike))
- Makefile Cleanup. [\#106](https://github.com/volcano-sh/volcano/pull/106) ([k82cn](https://github.com/k82cn))
- Updated README. [\#104](https://github.com/volcano-sh/volcano/pull/104) ([k82cn](https://github.com/k82cn))
- Added OWNERS. [\#103](https://github.com/volcano-sh/volcano/pull/103) ([k82cn](https://github.com/k82cn))
- Register global options when start [\#99](https://github.com/volcano-sh/volcano/pull/99) ([TommyLike](https://github.com/TommyLike))
- Add TF example [\#98](https://github.com/volcano-sh/volcano/pull/98) ([TommyLike](https://github.com/TommyLike))
- Updated kube-batch to release-0.4 [\#97](https://github.com/volcano-sh/volcano/pull/97) ([k82cn](https://github.com/k82cn))
- Added Queue design doc. [\#95](https://github.com/volcano-sh/volcano/pull/95) ([k82cn](https://github.com/k82cn))
- Clean code [\#92](https://github.com/volcano-sh/volcano/pull/92) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Refine Common Service intro in volcano intro image [\#91](https://github.com/volcano-sh/volcano/pull/91) ([TommyLike](https://github.com/TommyLike))
- Update volcano intro image [\#90](https://github.com/volcano-sh/volcano/pull/90) ([TommyLike](https://github.com/TommyLike))
- Abstract a common pod delete func [\#89](https://github.com/volcano-sh/volcano/pull/89) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Fix issue raised from previous MR [\#83](https://github.com/volcano-sh/volcano/pull/83) ([TommyLike](https://github.com/TommyLike))
- implement error code handling [\#81](https://github.com/volcano-sh/volcano/pull/81) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Move apis&cache into pkg/controllers folder [\#80](https://github.com/volcano-sh/volcano/pull/80) ([TommyLike](https://github.com/TommyLike))
- Exit code error handling [\#79](https://github.com/volcano-sh/volcano/pull/79) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Update scripts as per docker image [\#78](https://github.com/volcano-sh/volcano/pull/78) ([asifdxtreme](https://github.com/asifdxtreme))
- Fix task name default and validate it [\#76](https://github.com/volcano-sh/volcano/pull/76) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Cleanup makefile [\#75](https://github.com/volcano-sh/volcano/pull/75) ([TommyLike](https://github.com/TommyLike))
- Do not handle job update [\#74](https://github.com/volcano-sh/volcano/pull/74) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Use hostfile in MPI tests [\#73](https://github.com/volcano-sh/volcano/pull/73) ([TommyLike](https://github.com/TommyLike))
- Improve Quick Start Guide & update docker image repo [\#70](https://github.com/volcano-sh/volcano/pull/70) ([asifdxtreme](https://github.com/asifdxtreme))
- ignore already exist error [\#61](https://github.com/volcano-sh/volcano/pull/61) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- fix build error [\#60](https://github.com/volcano-sh/volcano/pull/60) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Support displaying log files [\#59](https://github.com/volcano-sh/volcano/pull/59) ([TommyLike](https://github.com/TommyLike))
- some minor cleanup and optmization [\#57](https://github.com/volcano-sh/volcano/pull/57) ([hzxuzhonghu](https://github.com/hzxuzhonghu))
- Upgrade kube-batch to 0.4.2 [\#53](https://github.com/volcano-sh/volcano/pull/53) ([k82cn](https://github.com/k82cn))
- Fix comment issue for MPI example MR [\#52](https://github.com/volcano-sh/volcano/pull/52) ([TommyLike](https://github.com/TommyLike))
- Add MPI example and tests [\#51](https://github.com/volcano-sh/volcano/pull/51) ([TommyLike](https://github.com/TommyLike))
- Support Plugins [\#50](https://github.com/volcano-sh/volcano/pull/50) ([TommyLike](https://github.com/TommyLike))
- Refresh volcano intro image [\#46](https://github.com/volcano-sh/volcano/pull/46) ([TommyLike](https://github.com/TommyLike))
- Add admission e2e test [\#45](https://github.com/volcano-sh/volcano/pull/45) ([TommyLike](https://github.com/TommyLike))
- Support setting queue name in job [\#39](https://github.com/volcano-sh/volcano/pull/39) ([TommyLike](https://github.com/TommyLike))
- Add ConfigMap for scheduler chart [\#38](https://github.com/volcano-sh/volcano/pull/38) ([TommyLike](https://github.com/TommyLike))
- Fix slice out of range error [\#37](https://github.com/volcano-sh/volcano/pull/37) ([TommyLike](https://github.com/TommyLike))
- Support TaskComplete in LifecyclePolicy [\#36](https://github.com/volcano-sh/volcano/pull/36) ([TommyLike](https://github.com/TommyLike))
- Update Readme with verification details [\#34](https://github.com/volcano-sh/volcano/pull/34) ([asifdxtreme](https://github.com/asifdxtreme))
- Fixed build error. [\#29](https://github.com/volcano-sh/volcano/pull/29) ([k82cn](https://github.com/k82cn))
- Support Travis CI  [\#28](https://github.com/volcano-sh/volcano/pull/28) ([TommyLike](https://github.com/TommyLike))
- Fix some chart issues [\#24](https://github.com/volcano-sh/volcano/pull/24) ([TommyLike](https://github.com/TommyLike))
- Fix volcano chart RBAC issue [\#23](https://github.com/volcano-sh/volcano/pull/23) ([TommyLike](https://github.com/TommyLike))
- Create default queue for e2e test. [\#22](https://github.com/volcano-sh/volcano/pull/22) ([TommyLike](https://github.com/TommyLike))
- Update Travis CI batch [\#21](https://github.com/volcano-sh/volcano/pull/21) ([asifdxtreme](https://github.com/asifdxtreme))
- Add Slack batch [\#20](https://github.com/volcano-sh/volcano/pull/20) ([asifdxtreme](https://github.com/asifdxtreme))
- Add issue templates [\#19](https://github.com/volcano-sh/volcano/pull/19) ([asifdxtreme](https://github.com/asifdxtreme))
- Ensure golint binary exists before linting [\#18](https://github.com/volcano-sh/volcano/pull/18) ([TommyLike](https://github.com/TommyLike))
- Code/add more tests [\#17](https://github.com/volcano-sh/volcano/pull/17) ([TommyLike](https://github.com/TommyLike))
- Updated README. [\#11](https://github.com/volcano-sh/volcano/pull/11) ([k82cn](https://github.com/k82cn))
- Added README about scheduler. [\#10](https://github.com/volcano-sh/volcano/pull/10) ([k82cn](https://github.com/k82cn))
- Added actions/plugins. [\#9](https://github.com/volcano-sh/volcano/pull/9) ([k82cn](https://github.com/k82cn))
- Fixed code fmt. [\#8](https://github.com/volcano-sh/volcano/pull/8) ([k82cn](https://github.com/k82cn))
- Codes/refactor controllers [\#6](https://github.com/volcano-sh/volcano/pull/6) ([TommyLike](https://github.com/TommyLike))
- Support helm chart [\#5](https://github.com/volcano-sh/volcano/pull/5) ([TommyLike](https://github.com/TommyLike))
- Fix some typos in code and document [\#3](https://github.com/volcano-sh/volcano/pull/3) ([TommyLike](https://github.com/TommyLike))
- Support Adm controllers [\#2](https://github.com/volcano-sh/volcano/pull/2) ([TommyLike](https://github.com/TommyLike))
- Rename hpw.cloud keyword to volcano.sh [\#1](https://github.com/volcano-sh/volcano/pull/1) ([TommyLike](https://github.com/TommyLike))
- Improve README.md documentation [\#48](https://github.com/volcano-sh/volcano/pull/48) ([quinton-hoole](https://github.com/quinton-hoole))