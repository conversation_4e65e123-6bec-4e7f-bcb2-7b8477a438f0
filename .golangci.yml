# options for analysis running
run:
  # default concurrency is a available CPU number
  concurrency: 4

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 10m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1

  # which dirs to skip: issues from them won't be reported;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but default dirs are skipped independently
  # from this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-dirs:
    - vendor
    - test
    - example
  skip-files:
    - .*_test.go

# output configuration options
output:
  # colored-line-number|line-number|json|tab|checkstyle|code-climate, default is "colored-line-number"
  format: colored-line-number

  # print lines of code with issue, default is true
  print-issued-lines: true

  # print linter name in the end of issue text, default is true
  print-linter-name: true

  # make issues output unique by line, default is true
  uniq-by-line: true

linters:
  # please, do not use `enable-all`: it's deprecated and will be removed soon.
  # inverted configuration with `enable-all` and `disable` is not scalable during updates of golangci-lint
  disable-all: true
  enable:
    # linters maintained by golang.org
    - gofmt
    - goimports
    - govet
    # linters default enabled by golangci-lint .
    - deadcode
    #- errcheck
    - gosimple
    - ineffassign
    - staticcheck
    - structcheck
    - typecheck
    - unused
    - varcheck
    # other linters supported by golangci-lint.
    #- gosec
    #- revive
    - whitespace

linters-settings:
  goimports:
    local-prefixes: volcano.sh
  staticcheck:
    checks:
      - "all"
      - "-SA1019"   # TODO(fix) Using a deprecated function, variable, constant or field


