apiVersion: batch.volcano.sh/v1alpha1
kind: Job
metadata:
  name: test-job
spec:
  minAvailable: 3
  schedulerName: volcano
  policies:
    - event: PodEvicted
      action: RestartJob
  plugins:
    ssh: []
    env: []
    svc: []
  maxRetry: 5
  queue: default
  # Comment out the following section to enable volumes for job input/output.
  #volumes:
  #  - mountPath: "/myinput"
  #  - mountPath: "/myoutput"
  #    volumeClaimName: "testvolumeclaimname"
  #    volumeClaim:
  #      accessModes: [ "ReadWriteOnce" ]
  #      storageClassName: "my-storage-class"
  #      resources:
  #        requests:
  #          storage: 1Gi
  tasks:
    - replicas: 6
      name: "default-nginx"
      template:
        metadata:
          name: web
        spec:
          containers:
            - image: nginx
              imagePullPolicy: IfNotPresent
              name: nginx
              resources:
                requests:
                  cpu: "1"
          restartPolicy: OnFailure
