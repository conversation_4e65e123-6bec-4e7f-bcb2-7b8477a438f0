# Volcano Governance

## Principles

The Volcano community adheres to the following principles:

- Open: Volcano is an open source community.
- Welcoming and respectful: See [Code of Conduct](https://github.com/cncf/foundation/blob/master/code-of-conduct.md).
- Transparent and accessible: Changes to the Volcano organization, Volcano code repositories, and CNCF related activities (e.g. level, involvement, etc) are done in public.
- Merit: Ideas and contributions are accepted according to their technical merit and alignment with
  project objectives, scope, and design principles.

## Expectations from Maintainers

Every one carries water...

Making a community work requires input/effort from everyone. Maintainers should actively
participate in Pull Request reviews. Maintainers are expected to respond to assigned Pull Requests
in a *reasonable* time frame, either providing insights, or assign the Pull Requests to other
maintainers.

Every Maintainer is listed in the
[MAINTAINERS](https://github.com/volcano-sh/volcano/blob/master/MAINTAINERS.md)
file, with their Github handle.

## Becoming a Maintainer

Please see the requirement [here](community-membership.md#maintainer)

## Changes in Maintainership

If a Maintainer feels she/he can not fulfill the "Expectations from Maintainers", they are free to
step down.

The Volcano organization will never forcefully remove a current Maintainer, unless a maintainer
fails to meet the principles of Volcano community,
or adhere to the [Code of Conduct](https://github.com/cncf/foundation/blob/master/code-of-conduct.md).

## Decision making process

Decisions are built on consensus between maintainers.
Proposals and ideas can either be submitted for agreement via a github issue or PR.

In general, we prefer that technical issues and maintainer membership are amicably worked out between the persons involved.
If a dispute cannot be decided independently, get a third-party maintainer (e.g. a mutual contact with some background
on the issue, but not involved in the conflict) to intercede.
If a dispute still cannot be decided, the project lead has the final say to decide an issue.

Decision making process should be transparent to adhere to
the principles of Volcano project.

All proposals, ideas, and decisions by maintainers or the project lead
should either be part of a github issue or PR.

## Github Project Administration

The __volcano__ GitHub project maintainers team reflects the list of Maintainers.

## Other Projects

The Volcano organization is open to receive new sub-projects under its umbrella. To accept a project
into the __Volcano__ organization, it has to meet the following criteria:

- Must be licensed under the terms of the Apache License v2.0
- Must be related to one or more scopes of the Volcano ecosystem:
  - Volcano project artifacts (website, deployments, CI, etc)
  - External plugins
  - Other AI/BigData related processing
- Must be supported by a Maintainer not associated or affiliated with the author(s) of the sub-projects

The submission process starts as a Pull Request or Issue on the
[volcano-sh/volcano](https://github.com/volcano-sh/volcano) repository with the required information
mentioned above. Once a project is accepted, it's considered a __CNCF sub-project under the umbrella
of Volcano__.

## Volcano and CNCF

Volcano is a CNCF project. As such, Volcano might be involved in CNCF (or other CNCF projects) related
marketing, events, or activities. Any maintainer could help driving the Volcano involvement, as long as
create a GitHub Pull Request to call for participation
from other maintainers. The `Call for Participation` should be kept open for no less than a week if time
permits, or a _reasonable_ time frame to allow maintainers to have a chance to volunteer.

## Code of Conduct

The [Volcano Code of Conduct](https://github.com/cncf/foundation/blob/master/code-of-conduct.md) is aligned with the CNCF Code of Conduct.

## Credits

Sections of this document have been borrowed from [CoreDns](https://github.com/coredns/coredns/blob/master/GOVERNANCE.md).